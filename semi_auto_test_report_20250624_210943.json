[{"test_name": "用户登录", "status": "PASS", "message": "成功登录用户: admin", "details": null, "timestamp": "2025-06-24T21:09:42.943654"}, {"test_name": "页面访问", "status": "PASS", "message": "手动导入订单页面可正常访问", "details": null, "timestamp": "2025-06-24T21:09:42.949807"}, {"test_name": "页面内容", "status": "PASS", "message": "页面标题正确显示", "details": null, "timestamp": "2025-06-24T21:09:42.949807"}, {"test_name": "关键元素-workflow-steps", "status": "PASS", "message": "元素存在于页面中", "details": null, "timestamp": "2025-06-24T21:09:42.950777"}, {"test_name": "关键元素-step1Panel", "status": "PASS", "message": "元素存在于页面中", "details": null, "timestamp": "2025-06-24T21:09:42.951811"}, {"test_name": "关键元素-step2Panel", "status": "PASS", "message": "元素存在于页面中", "details": null, "timestamp": "2025-06-24T21:09:42.951811"}, {"test_name": "关键元素-step3Panel", "status": "PASS", "message": "元素存在于页面中", "details": null, "timestamp": "2025-06-24T21:09:42.952777"}, {"test_name": "关键元素-startBtn", "status": "PASS", "message": "元素存在于页面中", "details": null, "timestamp": "2025-06-24T21:09:42.953808"}, {"test_name": "关键元素-pauseBtn", "status": "PASS", "message": "元素存在于页面中", "details": null, "timestamp": "2025-06-24T21:09:42.954776"}, {"test_name": "关键元素-stopBtn", "status": "PASS", "message": "元素存在于页面中", "details": null, "timestamp": "2025-06-24T21:09:42.954776"}, {"test_name": "API端点-邮箱配置API", "status": "PASS", "message": "端点可访问 (状态码: 200)", "details": null, "timestamp": "2025-06-24T21:09:42.960806"}, {"test_name": "API端点-本地附件扫描API", "status": "PASS", "message": "端点可访问 (状态码: 200)", "details": null, "timestamp": "2025-06-24T21:09:43.000445"}, {"test_name": "API端点-处理任务启动API", "status": "PASS", "message": "端点可访问 (状态码: 200)", "details": null, "timestamp": "2025-06-24T21:09:43.005419"}, {"test_name": "API端点-FT订单数据API", "status": "FAIL", "message": "异常状态码: 404", "details": null, "timestamp": "2025-06-24T21:09:43.008416"}, {"test_name": "API端点-CP订单数据API", "status": "FAIL", "message": "异常状态码: 404", "details": null, "timestamp": "2025-06-24T21:09:43.011416"}, {"test_name": "JS函数-startProcessing", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.019419"}, {"test_name": "JS函数-pauseProcessing", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.020417"}, {"test_name": "JS函数-stopProcessing", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.020417"}, {"test_name": "JS函数-switchStep", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.020417"}, {"test_name": "JS函数-scanAttachments", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.020417"}, {"test_name": "JS函数-processSelectedAttachments", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.021417"}, {"test_name": "JS函数-exportSelectedOrderData", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.021417"}, {"test_name": "JS函数-refreshOrderData", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.021417"}, {"test_name": "JS函数-testEmailConnection", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.021417"}, {"test_name": "JS函数-previewAttachments", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.022417"}, {"test_name": "JS函数-showAddEmailModal", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.023417"}, {"test_name": "JS函数-refreshEmailConfigs", "status": "PASS", "message": "函数已定义", "details": null, "timestamp": "2025-06-24T21:09:43.023417"}, {"test_name": "UI按钮-自动处理按钮", "status": "PASS", "message": "按钮元素存在", "details": null, "timestamp": "2025-06-24T21:09:43.031416"}, {"test_name": "UI按钮-分步处理按钮", "status": "PASS", "message": "按钮元素存在", "details": null, "timestamp": "2025-06-24T21:09:43.032418"}, {"test_name": "UI按钮-暂停按钮", "status": "PASS", "message": "按钮元素存在", "details": null, "timestamp": "2025-06-24T21:09:43.032418"}, {"test_name": "UI按钮-停止按钮", "status": "PASS", "message": "按钮元素存在", "details": null, "timestamp": "2025-06-24T21:09:43.032418"}, {"test_name": "UI按钮-扫描附件按钮", "status": "PASS", "message": "按钮元素存在", "details": null, "timestamp": "2025-06-24T21:09:43.032418"}, {"test_name": "UI按钮-处理选中按钮", "status": "PASS", "message": "按钮元素存在", "details": null, "timestamp": "2025-06-24T21:09:43.032418"}, {"test_name": "UI按钮-批量处理按钮", "status": "PASS", "message": "按钮元素存在", "details": null, "timestamp": "2025-06-24T21:09:43.032418"}, {"test_name": "UI表单-邮箱配置表单", "status": "PASS", "message": "表单元素存在", "details": null, "timestamp": "2025-06-24T21:09:43.033418"}, {"test_name": "UI表单-邮箱配置选择", "status": "PASS", "message": "表单元素存在", "details": null, "timestamp": "2025-06-24T21:09:43.033418"}, {"test_name": "UI表单-订单类型筛选", "status": "FAIL", "message": "表单元素缺失", "details": null, "timestamp": "2025-06-24T21:09:43.033418"}, {"test_name": "UI表单-日期范围筛选", "status": "FAIL", "message": "表单元素缺失", "details": null, "timestamp": "2025-06-24T21:09:43.033418"}]