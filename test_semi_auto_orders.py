#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动导入订单功能全面测试脚本
测试所有按键和功能的逻辑正确性和可用性
"""

import requests
import json
import time
import sys
from datetime import datetime

class SemiAutoOrdersTestSuite:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.logged_in = False
        
    def log_test(self, test_name, status, message="", details=None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'status': status,  # 'PASS', 'FAIL', 'SKIP'
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⏭️"
        print(f"{status_icon} {test_name}: {message}")
        
        if details:
            print(f"   详情: {details}")

    def login(self, username="admin", password="admin"):
        """登录系统"""
        try:
            print(f"🔐 尝试登录用户: {username}")

            # 获取登录页面
            login_page = self.session.get(f"{self.base_url}/auth/login")
            print(f"   登录页面状态: {login_page.status_code}")

            if login_page.status_code != 200:
                self.log_test("登录-获取登录页", "FAIL", f"无法访问登录页面: {login_page.status_code}")
                return False

            # 提交登录表单
            login_data = {
                'username': username,
                'password': password
            }

            response = self.session.post(f"{self.base_url}/auth/login",
                                       data=login_data,
                                       allow_redirects=True)

            print(f"   登录响应状态: {response.status_code}")
            print(f"   最终URL: {response.url}")

            # 尝试访问需要登录的页面来验证
            test_response = self.session.get(f"{self.base_url}/orders/semi-auto")
            print(f"   受保护页面状态: {test_response.status_code}")
            print(f"   页面包含登录表单: {'登录' in test_response.text}")
            print(f"   页面包含目标内容: {'手动导入订单' in test_response.text}")

            if test_response.status_code == 200 and "手动导入订单" in test_response.text:
                self.logged_in = True
                self.log_test("用户登录", "PASS", f"成功登录用户: {username}")
                return True
            elif "登录" in test_response.text:
                self.log_test("用户登录", "FAIL", "登录失败，仍显示登录页面")
                return False
            else:
                self.log_test("用户登录", "FAIL", f"登录后页面异常，状态码: {test_response.status_code}")
                return False

        except Exception as e:
            self.log_test("用户登录", "FAIL", f"登录异常: {str(e)}")
            return False

    def test_page_accessibility(self):
        """测试页面可访问性"""
        print("\n🔍 测试页面可访问性...")
        
        try:
            # 测试主页面
            response = self.session.get(f"{self.base_url}/orders/semi-auto")
            if response.status_code == 200:
                self.log_test("页面访问", "PASS", "手动导入订单页面可正常访问")
            else:
                self.log_test("页面访问", "FAIL", f"页面返回状态码: {response.status_code}")
                
            # 检查页面内容
            if "手动导入订单" in response.text:
                self.log_test("页面内容", "PASS", "页面标题正确显示")
            else:
                self.log_test("页面内容", "FAIL", "页面标题未正确显示")
                
            # 检查关键元素
            key_elements = [
                "workflow-steps",  # 工作流步骤
                "step1Panel",      # 步骤1面板
                "step2Panel",      # 步骤2面板  
                "step3Panel",      # 步骤3面板
                "startBtn",        # 开始按钮
                "pauseBtn",        # 暂停按钮
                "stopBtn"          # 停止按钮
            ]
            
            for element in key_elements:
                if element in response.text:
                    self.log_test(f"关键元素-{element}", "PASS", "元素存在于页面中")
                else:
                    self.log_test(f"关键元素-{element}", "FAIL", "元素未找到")
                    
        except Exception as e:
            self.log_test("页面访问", "FAIL", f"访问异常: {str(e)}")
    
    def test_api_endpoints(self):
        """测试API端点可用性"""
        print("\n🔍 测试API端点可用性...")
        
        # 需要测试的API端点
        api_endpoints = [
            ("/api/email_configs", "GET", "邮箱配置API"),
            ("/api/v2/orders/attachments/scan/local", "POST", "本地附件扫描API"),
            ("/api/v2/orders/processing/start", "POST", "处理任务启动API"),
            ("/api/v2/orders/data/ft_summary", "GET", "FT订单数据API"),
            ("/api/v2/orders/data/cp_summary", "GET", "CP订单数据API")
        ]
        
        for endpoint, method, description in api_endpoints:
            try:
                if method == "GET":
                    response = self.session.get(f"{self.base_url}{endpoint}")
                else:
                    response = self.session.post(f"{self.base_url}{endpoint}", 
                                               json={}, 
                                               headers={'Content-Type': 'application/json'})
                
                # 401表示需要认证，这是正常的
                if response.status_code in [200, 401, 403]:
                    self.log_test(f"API端点-{description}", "PASS", 
                                f"端点可访问 (状态码: {response.status_code})")
                else:
                    self.log_test(f"API端点-{description}", "FAIL", 
                                f"异常状态码: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"API端点-{description}", "FAIL", f"请求异常: {str(e)}")
    
    def test_javascript_functions(self):
        """测试JavaScript函数定义"""
        print("\n🔍 测试JavaScript函数定义...")
        
        try:
            response = self.session.get(f"{self.base_url}/orders/semi-auto")
            page_content = response.text
            
            # 需要检查的JavaScript函数
            js_functions = [
                "startProcessing",
                "pauseProcessing", 
                "stopProcessing",
                "switchStep",
                "scanAttachments",
                "processSelectedAttachments",
                "exportSelectedOrderData",
                "refreshOrderData",
                "testEmailConnection",
                "previewAttachments",
                "showAddEmailModal",
                "refreshEmailConfigs"
            ]
            
            for func in js_functions:
                if f"function {func}" in page_content or f"{func} =" in page_content:
                    self.log_test(f"JS函数-{func}", "PASS", "函数已定义")
                else:
                    self.log_test(f"JS函数-{func}", "FAIL", "函数未找到")
                    
        except Exception as e:
            self.log_test("JavaScript函数检查", "FAIL", f"检查异常: {str(e)}")
    
    def test_ui_elements(self):
        """测试UI元素完整性"""
        print("\n🔍 测试UI元素完整性...")
        
        try:
            response = self.session.get(f"{self.base_url}/orders/semi-auto")
            page_content = response.text
            
            # 检查按钮元素
            buttons = [
                ("startBtn", "自动处理按钮"),
                ("stepBtn", "分步处理按钮"),
                ("pauseBtn", "暂停按钮"),
                ("stopBtn", "停止按钮"),
                ("scanAttachmentsBtn", "扫描附件按钮"),
                ("processSelectedBtn", "处理选中按钮"),
                ("processBatchBtn", "批量处理按钮")
            ]
            
            for btn_id, description in buttons:
                if f'id="{btn_id}"' in page_content:
                    self.log_test(f"UI按钮-{description}", "PASS", "按钮元素存在")
                else:
                    self.log_test(f"UI按钮-{description}", "FAIL", "按钮元素缺失")
            
            # 检查表单元素
            forms = [
                ("emailConfigForm", "邮箱配置表单"),
                ("emailConfigSelect", "邮箱配置选择"),
                ("orderTypeFilter", "订单类型筛选"),
                ("dateRangeFilter", "日期范围筛选")
            ]
            
            for form_id, description in forms:
                if f'id="{form_id}"' in page_content:
                    self.log_test(f"UI表单-{description}", "PASS", "表单元素存在")
                else:
                    self.log_test(f"UI表单-{description}", "FAIL", "表单元素缺失")
                    
        except Exception as e:
            self.log_test("UI元素检查", "FAIL", f"检查异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始手动导入订单功能全面测试...")
        print("=" * 60)

        # 首先尝试登录
        if not self.login():
            print("❌ 无法登录系统，跳过需要认证的测试")
            return

        # 运行各项测试
        self.test_page_accessibility()
        self.test_api_endpoints()
        self.test_javascript_functions()
        self.test_ui_elements()

        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        skipped_tests = len([r for r in self.test_results if r['status'] == 'SKIP'])
        
        print(f"总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"⏭️ 跳过: {skipped_tests}")
        print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  - {result['test_name']}: {result['message']}")
        
        # 保存详细报告
        report_file = f"semi_auto_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存至: {report_file}")

if __name__ == "__main__":
    # 运行测试套件
    test_suite = SemiAutoOrdersTestSuite()
    test_suite.run_all_tests()
