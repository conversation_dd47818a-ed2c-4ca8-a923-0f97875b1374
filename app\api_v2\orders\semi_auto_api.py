#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
半自动订单处理API
提供Excel解析、附件管理、数据预览等功能
"""

import os
import json
import time
import threading
import uuid
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from flask import request, jsonify, current_app
from werkzeug.utils import secure_filename

from . import orders_bp
from app.services.universal_excel_parser import UniversalExcelParser
from app.services.summary_data_saver import summary_data_saver
from app.models import OrderData, EmailConfig, EmailAttachment
from app.models.ft_order_summary import FtOrderSummary
from app.models.cp_order_summary import CpOrderSummary
from app import db
from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
from app.services.enhanced_excel_parser import EnhancedExcelParser

# 初始化解析器
universal_parser = UniversalExcelParser()

# 全局任务状态管理
processing_tasks = {}
task_lock = threading.Lock()

# 添加线程池管理
from concurrent.futures import ThreadPoolExecutor

# 创建线程池
thread_pool = ThreadPoolExecutor(max_workers=5)

# 添加任务超时控制
TASK_TIMEOUT = 3600  # 1小时超时

class ProcessingTask:
    """处理任务类"""
    def __init__(self, task_id: str, user_id: str, mode: str):
        self.task_id = task_id
        self.user_id = user_id
        self.mode = mode
        self.status = 'pending'  # pending, running, paused, completed, failed
        self.progress = {'overall': 0, 'step': 0, 'message': '准备中...'}
        self.start_time = datetime.now()
        self.end_time = None
        self.results = {}
        self.error_message = None
        self.stop_requested = False
        self.pause_requested = False

    def to_dict(self):
        return {
            'task_id': self.task_id,
            'user_id': self.user_id,
            'mode': self.mode,
            'status': self.status,
            'progress': self.progress,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'results': self.results,
            'error_message': self.error_message
        }

@orders_bp.route('/attachments/scan', methods=['POST'])
def scan_attachments():
    """扫描邮箱中的Excel附件（预览模式）"""
    try:
        from app.models import EmailConfig
        # 已删除普通EmailProcessor，统一使用HighPerformanceEmailProcessor
        
        # 获取请求参数
        data = request.get_json() or {}
        email_config_id = data.get('email_config_id')
        scan_days = data.get('days', 7)  # 默认扫描7天
        
        # 如果没有指定邮箱配置，获取第一个可用的
        if not email_config_id:
            configs = EmailConfig.query.filter_by(enabled=True).all()
            if not configs:
                return jsonify({
                    'success': False,
                    'error': '没有找到可用的邮箱配置',
                    'message': '请先配置邮箱连接'
                }), 400
            config = configs[0]
        else:
            config = EmailConfig.query.get(email_config_id)
            if not config:
                return jsonify({
                    'success': False,
                    'error': '邮箱配置不存在',
                    'message': '指定的邮箱配置无效'
                }), 400
        
        current_app.logger.info(f"📧 开始扫描邮箱: {config.email} (最近 {scan_days} 天)")
        
        # 创建邮件处理器并连接
        # 使用高性能邮件处理器替代普通处理器
        config = EmailConfig.query.first()
        if not config:
            return jsonify({'success': False, 'error': '未找到邮箱配置'})
        processor = HighPerformanceEmailProcessor(config)
        if not processor.connect():
            return jsonify({
                'success': False,
                'error': '无法连接到邮箱',
                'message': f'连接邮箱 {config.email} 失败，请检查配置'
            }), 500
        
        try:
            # 预览附件（不下载）
            preview_result = processor.preview_attachments(days=scan_days)
            
            # 转换数据格式以匹配前端期望
            excel_files = []
            attachments = preview_result.get('attachments', [])
            
            for i, attachment in enumerate(attachments):
                excel_files.append({
                    'id': f"preview_{i}",  # 预览模式使用虚拟ID
                    'filename': attachment['filename'],
                    'filepath': None,  # 预览模式没有本地路径
                    'size': attachment['file_size'],
                    'received_time': attachment['receive_date'],
                    'status': 'preview',  # 标记为预览状态
                    'processed': False,
                    'process_result': None,
                    'process_message': None,
                    'sender': attachment['sender'],
                    'subject': attachment['subject'],
                    'file_exists': False,  # 预览模式文件还未下载
                    'folder': attachment['folder'],
                    'is_preview': True  # 标记这是预览数据
                })
            
            # 统计信息
            total_count = len(excel_files)
            
            current_app.logger.info(f"✅ 邮箱扫描完成: 找到 {total_count} 个Excel附件")
            
            return jsonify({
                'success': True,
                'data': excel_files,
                'total': total_count,
                'statistics': {
                    'total': total_count,
                    'processed': 0,  # 预览模式都是未处理
                    'pending': total_count,
                    'errors': 0
                },
                'preview_mode': True,  # 标记这是预览模式
                'email_config': {
                    'id': config.id,
                    'name': config.name,
                    'email': config.email
                },
                'scan_days': scan_days,
                'message': f'在邮箱中扫描到 {total_count} 个Excel附件 (预览模式)'
            })
            
        finally:
            processor.disconnect()
        
    except Exception as e:
        current_app.logger.error(f"扫描邮箱附件失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '扫描邮箱附件时发生错误'
        }), 500

@orders_bp.route('/attachments/scan/local', methods=['POST'])
def scan_local_attachments():
    """扫描本地已下载的Excel附件"""
    try:
        from app.api.routes import get_db_connection
        import pymysql.cursors
        
        # 连接到数据库
        db_connection = get_db_connection()
        cursor = db_connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询aps_system.email_attachments表
        cursor.execute("USE aps_system")
        cursor.execute("""
            SELECT 
                id,
                filename,
                file_path as filepath,
                file_size as size,
                receive_date as received_time,
                processed,
                process_result,
                process_message,
                sender,
                subject
            FROM email_attachments 
            ORDER BY receive_date DESC
        """)
        
        attachments = cursor.fetchall()
        
        # 转换数据格式以匹配前端期望
        excel_files = []
        for i, attachment in enumerate(attachments):
            # 确定状态
            if attachment['processed']:
                status = 'completed' if attachment['process_result'] == 'success' else 'error'
            else:
                status = 'pending'
            
            # 检查文件是否存在于文件系统
            file_exists = os.path.exists(attachment['filepath']) if attachment['filepath'] else False
            
            excel_files.append({
                'id': attachment['id'],
                'filename': attachment['filename'],
                'filepath': attachment['filepath'],
                'size': attachment['size'] or 0,
                'received_time': attachment['received_time'].isoformat() if attachment['received_time'] else '',
                'status': status,
                'processed': bool(attachment['processed']),
                'process_result': attachment['process_result'],
                'process_message': attachment['process_message'],
                'sender': attachment['sender'],
                'subject': attachment['subject'],
                'file_exists': file_exists,
                'folder': 'email_attachments',
                'is_preview': False  # 标记这是本地文件
            })
        
        cursor.close()
        db_connection.close()
        
        # 统计信息
        total_count = len(excel_files)
        processed_count = len([f for f in excel_files if f['processed']])
        pending_count = total_count - processed_count
        error_count = len([f for f in excel_files if f['process_result'] == 'error'])
        
        current_app.logger.info(f"扫描本地附件完成: 总数{total_count}, 已处理{processed_count}, 待处理{pending_count}")
        
        return jsonify({
            'success': True,
            'data': excel_files,
            'total': total_count,
            'statistics': {
                'total': total_count,
                'processed': processed_count,
                'pending': pending_count,
                'errors': error_count
            },
            'preview_mode': False,  # 标记这不是预览模式
            'message': f'扫描到 {total_count} 个本地附件 (已处理: {processed_count}, 待处理: {pending_count})'
        })
        
    except Exception as e:
        current_app.logger.error(f"扫描本地附件失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '扫描本地附件时发生错误'
        }), 500

@orders_bp.route('/attachments/<int:attachment_id>/process', methods=['POST'])
def process_single_attachment():
    """处理单个附件"""
    try:
        data = request.get_json() or {}
        file_path = data.get('filepath')
        
        if not file_path or not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': '文件不存在',
                'message': '指定的文件路径无效'
            }), 400
        
        # 使用通用解析器处理文件
        parse_result = universal_parser.parse_single_file(file_path)
        
        if parse_result.get('status') == 'success':
            template_type = parse_result.get('template_type', 'unknown')
            records_count = len(parse_result.get('data', []))
            
            return jsonify({
                'success': True,
                'data': {
                    'template_type': template_type,
                    'records_count': records_count,
                    'records': parse_result.get('data', []),
                    'confidence': parse_result.get('confidence', 0),
                    'processing_time': parse_result.get('processing_time', 0)
                },
                'message': f'成功解析 {records_count} 条记录'
            })
        else:
            return jsonify({
                'success': False,
                'error': parse_result.get('message', '解析失败'),
                'message': '文件解析失败'
            }), 400
            
    except Exception as e:
        current_app.logger.error(f"处理附件失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '处理附件时发生错误'
        }), 500

@orders_bp.route('/attachments/process-batch', methods=['POST'])
def process_batch_attachments():
    """批量处理附件 - 与自动处理任务关联"""
    try:
        data = request.get_json() or {}
        file_paths = data.get('file_paths', [])
        max_files = data.get('max_files', 10)
        task_id = data.get('task_id')  # 获取关联的任务ID
        
        if not file_paths:
            # 自动扫描文件
            attachments_dir = Path("downloads/email_attachments")
            file_paths = []
            
            if attachments_dir.exists():
                for file_path in attachments_dir.rglob("*.xls*"):
                    if file_path.is_file():
                        file_paths.append(str(file_path))
                        if len(file_paths) >= max_files:
                            break
        
        results = {
            'total_files': len(file_paths),
            'successful_parses': 0,
            'failed_parses': 0,
            'standard_orders': 0,
            'cp_orders': 0,
            'total_records': 0,
            'processing_results': []
        }
        
        for i, file_path in enumerate(file_paths):
            try:
                if not os.path.exists(file_path):
                    continue
                
                # 更新任务进度
                if task:
                    progress = 85 + int((i / len(file_paths)) * 10)  # 85-95%
                    task.progress = {'overall': progress, 'step': int((i / len(file_paths)) * 100), 'message': f'🔄 处理: {os.path.basename(file_path)}'}
                    
                    # 发送进度更新
                    try:
                        from app import socketio
                        socketio.emit('task_progress', {
                            'task_id': task_id,
                            'progress': progress,
                            'step_progress': int((i / len(file_paths)) * 100),
                            'message': f'🔄 处理: {os.path.basename(file_path)}',
                            'status': 'running'
                        })
                        socketio.emit('log', {
                            'level': 'info',
                            'message': f'🔄 批量处理: {os.path.basename(file_path)}'
                        })
                    except Exception as e:
                        print(f"Socket.IO发送失败: {e}")
                
                # 解析文件
                parse_result = universal_parser.parse_single_file(file_path)
                
                file_result = {
                    'file_path': file_path,
                    'filename': os.path.basename(file_path),
                    'index': i + 1,
                    'success': False,
                    'template_type': None,
                    'records_count': 0,
                    'error': None
                }
                
                if parse_result.get('status') == 'success':
                    file_result['success'] = True
                    file_result['template_type'] = parse_result.get('template_type', 'unknown')
                    file_result['records_count'] = len(parse_result.get('data', []))
                    file_result['confidence'] = parse_result.get('confidence', 0)
                    
                    # 保存到汇总表
                    save_result = summary_data_saver.save_orders_by_template(parse_result, file_path)
                    file_result['saved_count'] = save_result.get('saved_count', 0)
                    file_result['skipped_count'] = save_result.get('skipped_count', 0)
                    file_result['save_errors'] = save_result.get('errors', [])
                    
                    results['successful_parses'] += 1
                    results['total_records'] += file_result['records_count']
                    
                    if file_result['template_type'] == 'standard_template':
                        results['standard_orders'] += file_result['records_count']
                    elif file_result['template_type'] == 'cp_template':
                        results['cp_orders'] += file_result['records_count']
                else:
                    file_result['error'] = parse_result.get('message', '解析失败')
                    results['failed_parses'] += 1
                
                results['processing_results'].append(file_result)
                
            except Exception as e:
                file_result = {
                    'file_path': file_path,
                    'filename': os.path.basename(file_path),
                    'index': i + 1,
                    'success': False,
                    'error': str(e)
                }
                results['processing_results'].append(file_result)
                results['failed_parses'] += 1
        
        success_rate = (results['successful_parses'] / results['total_files'] * 100) if results['total_files'] > 0 else 0
        
        return jsonify({
            'success': True,
            'data': results,
            'message': f'批量处理完成：{results["successful_parses"]}/{results["total_files"]} 成功 ({success_rate:.1f}%)'
        })
        
    except Exception as e:
        current_app.logger.error(f"批量处理失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '批量处理时发生错误'
        }), 500

@orders_bp.route('/attachments/process', methods=['POST'])
def process_attachments():
    """处理选中的附件"""
    try:
        data = request.get_json() or {}
        attachment_ids = data.get('attachment_ids', [])
        parse_mode = data.get('parse_mode', 'auto')
        skip_duplicates = data.get('skip_duplicates', True)
        backup_original = data.get('backup_original', True)

        if not attachment_ids:
            return jsonify({
                'success': False,
                'error': '没有选择要处理的附件',
                'message': '请选择至少一个附件进行处理'
            }), 400

        # 获取附件信息
        from app.api.routes import get_db_connection
        import pymysql.cursors

        db_connection = get_db_connection()
        cursor = db_connection.cursor(pymysql.cursors.DictCursor)

        try:
            cursor.execute("USE aps_system")

            # 查询选中的附件
            placeholders = ','.join(['%s'] * len(attachment_ids))
            query = f"""
                SELECT id, filename, file_path, processed, process_result
                FROM email_attachments
                WHERE id IN ({placeholders})
            """
            cursor.execute(query, attachment_ids)
            attachments = cursor.fetchall()

            if not attachments:
                return jsonify({
                    'success': False,
                    'error': '未找到指定的附件',
                    'message': '选中的附件不存在或已被删除'
                }), 404

            # 处理每个附件
            results = {
                'total': len(attachments),
                'processed': 0,
                'skipped': 0,
                'failed': 0,
                'details': []
            }

            for attachment in attachments:
                file_path = attachment['file_path']
                filename = attachment['filename']

                # 检查文件是否存在
                if not os.path.exists(file_path):
                    results['failed'] += 1
                    results['details'].append({
                        'id': attachment['id'],
                        'filename': filename,
                        'status': 'failed',
                        'message': '文件不存在'
                    })
                    continue

                # 如果已处理且不允许重复处理
                if attachment['processed'] and skip_duplicates:
                    results['skipped'] += 1
                    results['details'].append({
                        'id': attachment['id'],
                        'filename': filename,
                        'status': 'skipped',
                        'message': '文件已处理，跳过'
                    })
                    continue

                try:
                    # 使用通用解析器处理文件
                    parse_result = universal_parser.parse_single_file(file_path)

                    if parse_result.get('status') == 'success':
                        # 保存解析结果
                        save_result = summary_data_saver.save_orders_by_template(parse_result, file_path)

                        results['processed'] += 1
                        results['details'].append({
                            'id': attachment['id'],
                            'filename': filename,
                            'status': 'success',
                            'message': f'成功处理 {len(parse_result.get("data", []))} 条记录',
                            'records_count': len(parse_result.get('data', [])),
                            'template_type': parse_result.get('template_type'),
                            'saved_count': save_result.get('saved_count', 0)
                        })

                        # 更新数据库中的处理状态
                        update_query = """
                            UPDATE email_attachments
                            SET processed = 1, process_result = 'success',
                                process_message = %s, process_date = NOW()
                            WHERE id = %s
                        """
                        cursor.execute(update_query, (f'成功处理 {len(parse_result.get("data", []))} 条记录', attachment['id']))
                    else:
                        results['failed'] += 1
                        error_msg = parse_result.get('message', '解析失败')
                        results['details'].append({
                            'id': attachment['id'],
                            'filename': filename,
                            'status': 'failed',
                            'message': error_msg
                        })

                        # 更新数据库中的处理状态
                        update_query = """
                            UPDATE email_attachments
                            SET processed = 1, process_result = 'error',
                                process_message = %s, process_date = NOW()
                            WHERE id = %s
                        """
                        cursor.execute(update_query, (error_msg, attachment['id']))

                except Exception as e:
                    results['failed'] += 1
                    error_msg = f'处理异常: {str(e)}'
                    results['details'].append({
                        'id': attachment['id'],
                        'filename': filename,
                        'status': 'failed',
                        'message': error_msg
                    })

                    # 更新数据库中的处理状态
                    update_query = """
                        UPDATE email_attachments
                        SET processed = 1, process_result = 'error',
                            process_message = %s, process_date = NOW()
                        WHERE id = %s
                    """
                    cursor.execute(update_query, (error_msg, attachment['id']))

            # 提交数据库更改
            db_connection.commit()

            success_rate = (results['processed'] / results['total'] * 100) if results['total'] > 0 else 0

            return jsonify({
                'success': True,
                'data': results,
                'message': f'批量处理完成：成功 {results["processed"]}，跳过 {results["skipped"]}，失败 {results["failed"]} (成功率: {success_rate:.1f}%)'
            })

        finally:
            cursor.close()
            db_connection.close()

    except Exception as e:
        current_app.logger.error(f"处理附件失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '处理附件时发生错误'
        }), 500

@orders_bp.route('/data/preview', methods=['GET'])
def preview_order_data():
    """预览订单汇总数据"""
    try:
        # 获取参数
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        table_type = request.args.get('table_type', 'ft_summary')  # ft_summary, cp_summary
        
        # 根据表类型选择模型
        if table_type == 'cp_summary':
            model_class = CpOrderSummary
            table_name = 'CP订单汇总表'
        else:
            model_class = FtOrderSummary
            table_name = 'FT订单汇总表'
        
        # 查询数据
        query = model_class.query.order_by(model_class.created_at.desc())
        total_count = query.count()
        orders = query.offset(offset).limit(limit).all()
        
        # 转换为字典
        formatted_data = [order.to_dict() for order in orders]
        
        # 获取汇总统计信息
        stats = summary_data_saver.get_summary_statistics()
        
        return jsonify({
            'success': True,
            'data': formatted_data,
            'stats': {
                'table_type': table_type,
                'table_name': table_name,
                'current_count': len(formatted_data),
                'total_count': total_count,
                'summary_stats': stats
            },
            'pagination': {
                'limit': limit,
                'offset': offset,
                'total': total_count,
                'has_more': (offset + limit) < total_count
            },
            'message': f'获取到 {len(formatted_data)} 条{table_name}记录'
        })
        
    except Exception as e:
        current_app.logger.error(f"预览数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '预览数据时发生错误'
        }), 500

@orders_bp.route('/data/export', methods=['POST'])
def export_order_data():
    """导出订单数据"""
    try:
        data = request.get_json() or {}
        export_format = data.get('format', 'excel')  # excel, csv, json
        filters = data.get('filters', {})
        
        # 构建查询
        standard_query = OrderData.query
        cp_query = CpOrderData.query
        
        # 应用筛选条件
        if filters.get('date_start'):
            start_date = datetime.fromisoformat(filters['date_start'])
            standard_query = standard_query.filter(OrderData.created_at >= start_date)
            cp_query = cp_query.filter(CpOrderData.created_at >= start_date)
        
        if filters.get('date_end'):
            end_date = datetime.fromisoformat(filters['date_end'])
            standard_query = standard_query.filter(OrderData.created_at <= end_date)
            cp_query = cp_query.filter(CpOrderData.created_at <= end_date)
        
        # 获取数据
        standard_orders = standard_query.all()
        cp_orders = cp_query.all()
        
        # 生成导出数据
        export_data = {
            'timestamp': datetime.now().isoformat(),
            'total_records': len(standard_orders) + len(cp_orders),
            'standard_orders': len(standard_orders),
            'cp_orders': len(cp_orders),
            'filters': filters,
            'data': {
                'standard': [
                    {
                        'order_number': order.order_number,
                        'product_name': order.product_name,
                        'chip_name': order.chip_name,
                        'lot_type': order.lot_type,
                        'created_at': order.created_at.isoformat() if order.created_at else None,
                        'source_file': getattr(order, 'source_file', '')
                    } for order in standard_orders
                ],
                'cp': [
                    {
                        'order_number': order.order_number,
                        'product_name': order.product_name,
                        'chip_name': order.chip_name,
                        'processing_pieces': getattr(order, 'processing_pieces', 0),
                        'created_at': order.created_at.isoformat() if order.created_at else None,
                        'source_file': getattr(order, 'source_file', '')
                    } for order in cp_orders
                ]
            }
        }
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'order_export_{timestamp}.json'
        
        # 保存到exports目录
        exports_dir = Path('static/exports')
        exports_dir.mkdir(exist_ok=True)
        
        export_path = exports_dir / filename
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        return jsonify({
            'success': True,
            'data': {
                'filename': filename,
                'download_url': f'/static/exports/{filename}',
                'file_size': export_path.stat().st_size,
                'total_records': export_data['total_records']
            },
            'message': f'成功导出 {export_data["total_records"]} 条记录'
        })
        
    except Exception as e:
        current_app.logger.error(f"导出数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '导出数据时发生错误'
        }), 500

@orders_bp.route('/processing/status', methods=['GET'])
def get_processing_status():
    """获取处理状态"""
    try:
        # 扫描文件状态
        attachments_dir = Path("downloads/email_attachments")
        total_files = 0
        
        if attachments_dir.exists():
            total_files = len(list(attachments_dir.rglob("*.xls*")))
        
        # 数据库统计（避免cp_order_data表不存在的问题）
        try:
            standard_count = OrderData.query.count()
        except Exception as e:
            current_app.logger.warning(f"OrderData查询失败: {e}")
            standard_count = 0
        
        try:
            cp_count = CpOrderSummary.query.count()
        except Exception as e:
            current_app.logger.warning(f"CpOrderSummary查询失败: {e}")
            cp_count = 0
        
        status = {
            'files': {
                'total': total_files,
                'processed': standard_count + cp_count,
                'pending': max(0, total_files - (standard_count + cp_count)),
                'error': 0
            },
            'records': {
                'total': standard_count + cp_count,
                'standard': standard_count,
                'cp': cp_count,
                'valid': standard_count + cp_count,
                'duplicates': 0
            },
            'processing': {
                'active': False,
                'task_id': None,
                'progress': 100 if total_files > 0 and (standard_count + cp_count) >= total_files else 0,
                'message': '就绪'
            }
        }
        
        return jsonify({
            'success': True,
            'data': status,
            'message': '获取状态成功'
        })
        
    except Exception as e:
        current_app.logger.error(f"获取状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取状态时发生错误'
        }), 500

@orders_bp.route('/parsing/validate', methods=['POST'])
def validate_excel_file():
    """验证Excel文件格式"""
    try:
        data = request.get_json() or {}
        file_path = data.get('filepath')
        
        if not file_path or not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': '文件不存在',
                'message': '指定的文件路径无效'
            }), 400
        
        # 进行模板识别但不解析数据
        template_result = universal_parser.identify_template(file_path)
        
        return jsonify({
            'success': True,
            'data': {
                'template_type': template_result.get('template_type', 'unknown'),
                'confidence': template_result.get('confidence', 0),
                'supported': template_result.get('template_type') in ['standard_template', 'cp_template'],
                'file_info': {
                    'name': os.path.basename(file_path),
                    'size': os.path.getsize(file_path),
                    'modified': datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                }
            },
            'message': '文件验证完成'
        })
        
    except Exception as e:
        current_app.logger.error(f"验证文件失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '验证文件时发生错误'
        }), 500

@orders_bp.route('/processing/start', methods=['POST'])
def start_processing():
    """启动订单处理任务"""
    try:
        data = request.get_json() or {}
        
        # 获取参数
        mode = data.get('mode', 'auto')  # auto, step
        email_config_ids = data.get('email_configs', [])
        parse_settings = data.get('parse_settings', {})
        
        current_app.logger.info(f"启动订单处理任务: mode={mode}, email_configs={email_config_ids}")
        
        # 生成任务ID
        task_id = f"order_proc_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        # 创建任务对象
        task = ProcessingTask(task_id, 'current_user', mode)
        
        with task_lock:
            # 检查是否有太多任务
            if len(processing_tasks) >= 5:
                return jsonify({
                    'success': False,
                    'error': '系统中已有太多任务，请稍后再试'
                }), 429
            processing_tasks[task_id] = task
        
        # 获取当前应用实例传递给线程
        app = current_app._get_current_object()
        
        # 使用线程池提交任务
        future = thread_pool.submit(_execute_processing_task, task, email_config_ids, parse_settings, app)
        
        # 添加任务完成回调
        def task_done_callback(future):
            try:
                # 处理任务完成
                if task.status != 'completed' and task.status != 'failed':
                    task.status = 'completed' if not future.exception() else 'failed'
                    if future.exception():
                        task.error_message = str(future.exception())
                    task.end_time = datetime.now()
            except Exception as e:
                current_app.logger.error(f"任务完成回调出错: {e}")
        
        future.add_done_callback(task_done_callback)
        
        return jsonify({
            'success': True,
            'message': '订单处理任务已启动',
            'task_id': task_id
        })
        
    except Exception as e:
        current_app.logger.error(f"启动订单处理任务失败: {e}")
        return jsonify({
            'success': False,
            'error': f'启动失败: {str(e)}'
        }), 500

@orders_bp.route('/processing/<task_id>/pause', methods=['POST'])
def pause_processing(task_id):
    """暂停处理任务"""
    try:
        with task_lock:
            task = processing_tasks.get(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'error': '任务不存在'
                }), 404
            
            if task.status != 'running':
                return jsonify({
                    'success': False,
                    'error': f'任务状态不允许暂停: {task.status}'
                }), 400
            
            task.pause_requested = True
            task.status = 'paused'
        
        current_app.logger.info(f"任务 {task_id} 已暂停")
        
        return jsonify({
            'success': True,
            'message': '任务已暂停',
            'task_id': task_id
        })
        
    except Exception as e:
        current_app.logger.error(f"暂停任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@orders_bp.route('/processing/<task_id>/stop', methods=['POST'])
def stop_processing(task_id):
    """停止处理任务"""
    try:
        with task_lock:
            task = processing_tasks.get(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'error': '任务不存在'
                }), 404
            
            task.stop_requested = True
            task.status = 'stopped'
            task.end_time = datetime.now()
        
        current_app.logger.info(f"任务 {task_id} 已停止")
        
        return jsonify({
            'success': True,
            'message': '任务已停止',
            'task_id': task_id
        })
        
    except Exception as e:
        current_app.logger.error(f"停止任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@orders_bp.route('/processing/<task_id>/resume', methods=['POST'])
def resume_processing(task_id):
    """恢复处理任务"""
    try:
        with task_lock:
            task = processing_tasks.get(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'error': '任务不存在'
                }), 404

            if task.status != 'paused':
                return jsonify({
                    'success': False,
                    'error': f'任务状态不允许恢复: {task.status}'
                }), 400

            task.pause_requested = False
            task.status = 'running'

        current_app.logger.info(f"任务 {task_id} 已恢复")

        return jsonify({
            'success': True,
            'message': '任务已恢复',
            'task_id': task_id
        })

    except Exception as e:
        current_app.logger.error(f"恢复任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@orders_bp.route('/processing/<task_id>/status', methods=['GET'])
def get_task_status(task_id):
    """获取任务状态"""
    try:
        with task_lock:
            task = processing_tasks.get(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'error': '任务不存在'
                }), 404
        
        return jsonify({
            'success': True,
            'data': task.to_dict(),
            'message': '获取任务状态成功'
        })
        
    except Exception as e:
        current_app.logger.error(f"获取任务状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@orders_bp.route('/processing/schedule', methods=['POST'])
def schedule_processing():
    """定时执行处理任务"""
    try:
        data = request.get_json() or {}
        schedule_time = data.get('schedule_time')
        mode = data.get('mode', 'auto')
        email_configs = data.get('email_configs', [])
        parse_settings = data.get('parse_settings', {})

        if not schedule_time:
            return jsonify({
                'success': False,
                'error': '缺少定时执行时间'
            }), 400

        # 这里可以集成定时任务系统，比如Celery或APScheduler
        # 目前返回成功响应表示API可用
        return jsonify({
            'success': True,
            'message': f'定时任务已设置，将在 {schedule_time} 执行',
            'schedule_time': schedule_time,
            'mode': mode
        })

    except Exception as e:
        current_app.logger.error(f"设置定时任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@orders_bp.route('/processing/tasks', methods=['GET'])
def get_processing_tasks():
    """获取所有处理任务列表"""
    try:
        with task_lock:
            tasks_list = []
            for task_id, task in processing_tasks.items():
                tasks_list.append(task.to_dict())

        return jsonify({
            'success': True,
            'data': tasks_list,
            'total': len(tasks_list),
            'message': f'获取到 {len(tasks_list)} 个任务'
        })

    except Exception as e:
        current_app.logger.error(f"获取任务列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def send_socketio_message(event, data, retry=3):
    """发送Socket.IO消息，支持重试"""
    try:
        from app import socketio
        socketio.emit(event, data)
        return True
    except Exception as e:
        if retry > 0:
            print(f"Socket.IO发送失败，尝试重试 ({retry}): {e}")
            time.sleep(0.5)
            return send_socketio_message(event, data, retry-1)
        else:
            print(f"Socket.IO发送最终失败: {e}")
            return False

def _execute_processing_task(task, email_config_ids, parse_settings, app):
    """执行订单处理任务的实际逻辑 - 统一使用高性能邮件处理器"""
    try:
        import os
        from datetime import datetime
        from app.models import EmailConfig
        from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
        from app.services.enhanced_excel_parser import EnhancedExcelParser
        
        task.status = 'running'
        task.start_time = datetime.now()
        task.progress = {'overall': 0, 'step': 0, 'message': '开始处理...'}
        print(f"🚀 开始执行订单处理任务 {task.task_id} (使用高性能邮件处理器)")
        
        # 发送任务开始消息
        send_socketio_message('task_progress', {
            'task_id': task.task_id,
            'progress': 0,
            'step_progress': 0,
            'message': '🚀 开始处理...',
            'status': 'running'
        })
        
        send_socketio_message('log', {
            'level': 'info',
            'message': f'🚀 开始执行订单处理任务: {task.task_id}'
        })
        
        # 获取邮箱配置
        configs = []
        for config_id in email_config_ids:
            config = EmailConfig.query.get(config_id)
            if config and config.enabled:
                configs.append(config)
        
        if not configs:
            raise Exception("没有找到可用的邮箱配置")
        
        print(f"📧 找到 {len(configs)} 个邮箱配置")
        
        total_attachments = 0
        processed_files = 0
        
        # 处理每个邮箱配置
        for i, config in enumerate(configs):
            if task.stop_requested:
                print("收到停止请求，终止处理")
                break
                
            # 更新进度
            config_progress = int((i / len(configs)) * 80)  # 80%用于邮件处理
            task.progress = {
                'overall': config_progress,
                'step': 0,
                'message': f'🚀 高性能邮件处理: {config.email}'
            }
            
            print(f"📧 开始处理邮箱配置: {config.name} ({config.email})")
            
            # 发送进度更新
            send_socketio_message('task_progress', {
                'task_id': task.task_id,
                'progress': config_progress,
                'step_progress': 0,
                'message': f'🚀 高性能邮件处理: {config.email}',
                'status': 'running'
            })
            
            send_socketio_message('log', {
                'level': 'info',
                'message': f'🚀 高性能邮件处理: {config.email}'
            })
            
            try:
                # 使用高性能邮件处理器
                processor = HighPerformanceEmailProcessor(config)
                
                # 设置进度回调
                def progress_callback(data):
                    # 更新任务进度
                    current_config_progress = int((i / len(configs)) * 80)
                    email_progress = int(data.get('progress', 0) * 0.8)  # 邮件处理占80%
                    overall_progress = current_config_progress + email_progress
                    
                    task.progress = {
                        'overall': min(overall_progress, 80),
                        'step': data.get('progress', 0),
                        'message': f"🚀 {data['message']}"
                    }
                    
                    # 发送Socket.IO消息到前端
                    send_socketio_message('task_progress', {
                        'task_id': task.task_id,
                        'progress': task.progress['overall'],
                        'step_progress': task.progress['step'],
                        'message': task.progress['message'],
                        'status': task.status
                    })
                    
                    # 同时发送日志消息
                    send_socketio_message('log', {
                        'level': 'info',
                        'message': task.progress['message']
                    })
                
                processor.set_progress_callback(progress_callback)
                
                # 快速处理邮件（使用配置的天数）
                fetch_days = getattr(config, 'fetch_days', 3)
                print(f"📅 抓取最近 {fetch_days} 天的邮件")
                start_time = time.time()
                fetch_result = processor.process_fast(days=fetch_days)
                process_time = time.time() - start_time
                
                if fetch_result.get('success'):
                    downloaded = fetch_result.get('processed', 0)
                    skipped = fetch_result.get('skipped', 0)
                    total_emails = fetch_result.get('total', 0)
                    
                    print(f"📊 高性能处理结果:")
                    print(f"   - 处理邮件: {total_emails} 封")
                    print(f"   - 下载附件: {downloaded} 个")
                    print(f"   - 跳过文件: {skipped} 个")
                    print(f"   - 处理时间: {process_time:.2f}秒")
                    print(f"   - 处理速度: {total_emails/process_time:.1f} 邮件/秒" if process_time > 0 else "   - 处理速度: ∞ 邮件/秒")
                    
                    total_attachments += downloaded
                    
                    # 发送详细的阶段汇总
                    send_socketio_message('log', {
                        'level': 'success',
                        'message': f'✅ {config.email}: 下载 {downloaded} 个附件 (速度: {total_emails/process_time:.1f} 邮件/秒)' if process_time > 0 else f'✅ {config.email}: 下载 {downloaded} 个附件'
                    })
                    
                    send_socketio_message('log', {
                        'level': 'info',
                        'message': f'📊 阶段汇总 - 邮件处理: 成功处理 {total_emails} 封邮件，下载 {downloaded} 个附件，跳过 {skipped} 个文件'
                    })
                else:
                    error_msg = fetch_result.get('error', '未知错误')
                    print(f"❌ 高性能处理失败: {error_msg}")
                    
                    # 发送错误信息
                    send_socketio_message('log', {
                        'level': 'error',
                        'message': f'❌ 邮件处理失败: {config.email} - {error_msg}'
                    })
                    
                    # 回退到原处理器
                    print("📋 高性能处理器任务完成")
                    send_socketio_message('log', {
                        'level': 'info', 
                        'message': f'📋 高性能处理器已完成: {config.email}'
                    })
                    # 不再需要fallback处理器，高性能处理器已足够强大
                    
            except Exception as e:
                print(f"❌ 处理邮箱配置出错: {e}")
        
        # Excel解析阶段 (80-95%)
        task.progress = {'overall': 80, 'step': 0, 'message': '📊 开始解析Excel文件...'}
        print("📊 开始Excel文件解析阶段...")
        
        # 发送邮件处理阶段完成汇总
        send_socketio_message('log', {
            'level': 'info',
            'message': f'📊 邮件处理阶段完成汇总: 处理了 {len(configs)} 个邮箱配置，总共下载 {total_attachments} 个附件'
        })
        
        send_socketio_message('task_progress', {
            'task_id': task.task_id,
            'progress': 80,
            'step_progress': 0,
            'message': '📊 开始解析Excel文件...',
            'status': 'running'
        })
        
        send_socketio_message('log', {
            'level': 'info',
            'message': '📊 开始Excel文件解析阶段...'
        })
        
        try:
            # 直接调用批量处理逻辑，而不是API
            print("📄 开始批量处理Excel文件...")
            
            # 自动扫描文件
            from pathlib import Path
            attachments_dir = Path("downloads/email_attachments")
            file_paths = []
            
            if attachments_dir.exists():
                for file_path in attachments_dir.rglob("*.xls*"):
                    if file_path.is_file():
                        file_paths.append(str(file_path))
                        if len(file_paths) >= 20:  # 限制文件数量
                            break
            
            print(f"📄 找到 {len(file_paths)} 个Excel文件")
            
            # 发送文件扫描汇总
            send_socketio_message('log', {
                'level': 'info',
                'message': f'📄 文件扫描完成: 找到 {len(file_paths)} 个Excel文件等待解析'
            })
            
            # 统计变量
            excel_success_count = 0
            excel_failed_count = 0
            total_records_saved = 0
            
            # 批量处理文件
            for i, file_path in enumerate(file_paths):
                if task.stop_requested:
                    break
                    
                progress = 80 + int((i / len(file_paths)) * 15)  # 80-95%
                task.progress = {'overall': progress, 'step': int((i / len(file_paths)) * 100), 'message': f'📄 解析: {os.path.basename(file_path)}'}
                
                # 发送进度更新
                send_socketio_message('task_progress', {
                    'task_id': task.task_id,
                    'progress': progress,
                    'step_progress': int((i / len(file_paths)) * 100),
                    'message': f'📄 解析: {os.path.basename(file_path)}',
                    'status': 'running'
                })
                
                send_socketio_message('log', {
                    'level': 'info',
                    'message': f'📄 批量处理: {os.path.basename(file_path)}'
                })
                
                try:
                    if os.path.exists(file_path):
                        # 导入并使用universal_parser解析文件
                        from app.services.universal_excel_parser import UniversalExcelParser
                        from app.services.summary_data_saver import SummaryDataSaver
                        
                        parser = UniversalExcelParser()
                        saver = SummaryDataSaver()
                        
                        parse_result = parser.parse_single_file(file_path)
                        
                        if parse_result.get('status') == 'success':
                            processed_files += 1
                            excel_success_count += 1
                            print(f"✅ 文件解析成功: {os.path.basename(file_path)}")
                            
                            # 保存到汇总表
                            save_result = saver.save_orders_by_template(parse_result, file_path)
                            saved_count = save_result.get('saved_count', 0)
                            total_records_saved += saved_count
                            print(f"📊 数据保存: {saved_count} 条记录")
                            
                            # 发送成功消息
                            send_socketio_message('log', {
                                'level': 'success',
                                'message': f'✅ 解析成功: {os.path.basename(file_path)} ({saved_count} 条记录)'
                            })
                        else:
                            excel_failed_count += 1
                            error_msg = parse_result.get('message', '未知解析错误')
                            print(f"⚠️ 文件解析失败: {os.path.basename(file_path)} - {error_msg}")
                            
                            # 发送解析失败消息
                            send_socketio_message('log', {
                                'level': 'warning',
                                'message': f'⚠️ 解析失败: {os.path.basename(file_path)} - {error_msg}'
                            })
                    else:
                        excel_failed_count += 1
                        # 发送文件不存在消息
                        send_socketio_message('log', {
                            'level': 'error',
                            'message': f'❌ 文件不存在: {os.path.basename(file_path)}'
                        })
                            
                except Exception as e:
                    excel_failed_count += 1
                    print(f"❌ 处理文件出错: {os.path.basename(file_path)}: {e}")
                    
                    # 发送文件处理错误消息
                    send_socketio_message('log', {
                        'level': 'error',
                        'message': f'❌ 处理出错: {os.path.basename(file_path)} - {str(e)}'
                    })
            
            # 发送Excel解析阶段汇总
            send_socketio_message('log', {
                'level': 'info',
                'message': f'📊 Excel解析阶段完成汇总: 成功解析 {excel_success_count} 个文件，失败 {excel_failed_count} 个，总共保存 {total_records_saved} 条记录'
            })
                
        except Exception as e:
            print(f"❌ Excel解析阶段出错: {e}")
            
            # 发送Excel解析阶段错误信息
            send_socketio_message('log', {
                'level': 'error',
                'message': f'❌ Excel解析阶段严重错误: {str(e)}'
            })
        
        # 提交数据库变更
        try:
            db.session.commit()
            print("✅ 数据库变更已提交")
            
            # 发送数据库提交成功信息
            send_socketio_message('log', {
                'level': 'success',
                'message': '✅ 数据库变更已提交，数据已安全保存'
            })
                
        except Exception as e:
            print(f"❌ 数据库提交失败: {e}")
            db.session.rollback()
            
            # 发送数据库提交失败信息
            send_socketio_message('log', {
                'level': 'error',
                'message': f'❌ 数据库提交失败: {str(e)}，已回滚数据'
            })
        
        # 数据汇总阶段 (95-98%)
        task.progress = {'overall': 95, 'step': 0, 'message': '📊 开始数据汇总...'}
        print("📊 开始数据汇总阶段...")
        
        # 发送进度更新
        send_socketio_message('task_progress', {
            'task_id': task.task_id,
            'progress': 95,
            'step_progress': 0,
            'message': '📊 开始数据汇总...',
            'status': 'running'
        })
        
        send_socketio_message('log', {
            'level': 'info',
            'message': '📊 开始数据汇总阶段...'
        })
        
        # 统计处理结果
        try:
            total_processed_attachments = EmailAttachment.query.filter_by(processed=True).count()
            successful_count = processed_files  # 使用实际处理的文件数
            
            print(f"📊 数据汇总: 总附件 {total_processed_attachments}, 已处理 {processed_files}, 成功 {successful_count}")
            
            # 统计各类型数据
            from app.models.ft_order_summary import FtOrderSummary
            from app.models.cp_order_summary import CpOrderSummary
            
            ft_count = FtOrderSummary.query.count()
            cp_count = CpOrderSummary.query.count()
            
            # 发送详细的数据统计汇总
            send_socketio_message('task_progress', {
                'task_id': task.task_id,
                'progress': 96,
                'step_progress': 50,
                'message': f'📊 统计完成: {successful_count} 个成功解析',
                'status': 'running'
            })
            
            send_socketio_message('log', {
                'level': 'info',
                'message': f'📊 数据统计汇总: 邮件附件 {total_processed_attachments} 个, 成功解析文件 {successful_count} 个'
            })
            
            send_socketio_message('log', {
                'level': 'info',
                'message': f'📊 订单数据汇总: FT订单 {ft_count} 条, CP订单 {cp_count} 条，总计 {ft_count + cp_count} 条订单记录'
            })
        except Exception as e:
            print(f"❌ 数据汇总阶段出错: {e}")
            traceback.print_exc()
            
            # 发送数据汇总错误信息
            send_socketio_message('log', {
                'level': 'error',
                'message': f'❌ 数据汇总出错: {str(e)}'
            })
            
            # 使用默认值继续执行
            total_processed_attachments = 0
            successful_count = processed_files
        
        # 数据导出阶段 (98-100%)
        task.progress = {'overall': 98, 'step': 0, 'message': '📤 准备数据导出...'}
        print("📤 开始数据导出阶段...")
        
        # 发送进度更新
        send_socketio_message('task_progress', {
            'task_id': task.task_id,
            'progress': 98,
            'step_progress': 0,
            'message': '📤 准备数据导出...',
            'status': 'running'
        })
        
        send_socketio_message('log', {
            'level': 'info',
            'message': '📤 开始数据导出阶段...'
        })
        
        # 最后更新进度
        send_socketio_message('task_progress', {
            'task_id': task.task_id,
            'progress': 99,
            'step_progress': 80,
            'message': '📤 生成导出文件...',
            'status': 'running'
        })
        
        send_socketio_message('log', {
            'level': 'info',
            'message': '📤 正在生成导出文件...'
        })
        
        print("📤 数据导出准备完成")
        
        # 任务完成
        task.status = 'completed'
        task.end_time = datetime.now()
        execution_time = (task.end_time - task.start_time).total_seconds()
        
        task.progress = {
            'overall': 100,
            'step': 100,
            'message': '🎉 处理完成'
        }
        
        # 发送最终完成汇总
        send_socketio_message('task_progress', {
            'task_id': task.task_id,
            'progress': 100,
            'step_progress': 100,
            'message': '🎉 任务完成',
            'status': 'completed'
        })
        
        # 发送详细的最终汇总
        send_socketio_message('log', {
            'level': 'success',
            'message': f'🎉 任务完成总结:'
        })
        
        send_socketio_message('log', {
            'level': 'info',
            'message': f'📧 邮件处理: 处理 {len(configs)} 个邮箱配置，下载 {total_attachments} 个附件'
        })
        
        send_socketio_message('log', {
            'level': 'info', 
            'message': f'📄 文件解析: 解析 {excel_success_count or processed_files} 个Excel文件，保存 {total_records_saved if "total_records_saved" in locals() else "N/A"} 条记录'
        })
        
        send_socketio_message('log', {
            'level': 'info',
            'message': f'⏱️ 执行时间: {execution_time:.2f} 秒'
        })
        
        send_socketio_message('log', {
            'level': 'success',
            'message': '✅ 所有数据已安全保存到数据库'
        })
        
        task.results = {
            'processed_configs': len(configs),
            'total_attachments': total_attachments,
            'processed_files': processed_files,
            'duration': (task.end_time - task.start_time).total_seconds()
        }
        
        print(f"🎉 订单处理任务完成: {task.results}")
        
        # 发送任务完成消息
        send_socketio_message('task_progress', {
            'task_id': task.task_id,
            'progress': 100,
            'step_progress': 100,
            'message': '🎉 处理完成',
            'status': 'completed'
        })
        
        send_socketio_message('task_complete', {
            'task_id': task.task_id,
            'results': task.results,
            'message': '🎉 订单处理任务完成'
        })
        
        send_socketio_message('log', {
            'level': 'success',
            'message': f'🎉 订单处理任务完成: 处理了 {total_attachments} 个附件，解析了 {processed_files} 个文件'
        })
        
    except Exception as e:
        print(f"订单处理任务执行出错: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        task.status = 'failed'
        task.error_message = str(e)
        task.end_time = datetime.now()
        
        # 分析错误发生的阶段
        current_progress = task.progress.get('overall', 0)
        error_stage = "未知阶段"
        if current_progress < 10:
            error_stage = "初始化阶段"
        elif current_progress < 80:
            error_stage = "邮件处理阶段"
        elif current_progress < 95:
            error_stage = "Excel解析阶段"
        elif current_progress < 98:
            error_stage = "数据汇总阶段"
        else:
            error_stage = "数据导出阶段"
        
        # 发送详细的错误消息
        send_socketio_message('task_progress', {
            'task_id': task.task_id,
            'progress': current_progress,
            'step_progress': task.progress.get('step', 0),
            'message': f'❌ 处理失败: {str(e)}',
            'status': 'failed'
        })
        
        send_socketio_message('log', {
            'level': 'error',
            'message': f'❌ 任务执行失败汇总:'
        })
        
        send_socketio_message('log', {
            'level': 'error',
            'message': f'💥 失败阶段: {error_stage} (进度: {current_progress}%)'
        })
        
        send_socketio_message('log', {
            'level': 'error',
            'message': f'🔍 错误详情: {str(e)}'
        })
        
        send_socketio_message('log', {
            'level': 'warning',
            'message': '⚠️ 建议检查网络连接、邮箱配置和Excel文件格式'
        })

@orders_bp.route('/test-socketio', methods=['POST'])
def test_socketio():
    """测试Socket.IO连接的API端点"""
    try:
        from app import socketio
        import time
        
        # 发送测试进度更新
        socketio.emit('task_progress', {
            'task_id': f'api_test_{int(time.time())}',
            'progress': 75,
            'step_progress': 85,
            'message': '🧪 API测试消息 - Socket.IO连接正常！',
            'status': 'running'
        })
        
        # 发送测试日志
        socketio.emit('log', {
            'level': 'success',
            'message': '✅ Socket.IO API测试成功 - 连接工作正常！'
        })
        
        current_app.logger.info("Socket.IO测试消息已发送")
        
        return jsonify({
            'success': True,
            'message': 'Socket.IO测试消息已发送'
        })
        
    except Exception as e:
        current_app.logger.error(f"Socket.IO测试失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500 

# ===== 授权码保存功能 =====
@orders_bp.route('/email-configs/<int:config_id>/save-auth', methods=['POST'])
def save_email_auth_code(config_id):
    """保存邮箱授权码（加密存储）"""
    try:
        data = request.get_json()
        auth_code = data.get('auth_code', '').strip()
        
        if not auth_code:
            return jsonify({
                'success': False,
                'error': '授权码不能为空'
            }), 400
            
        # 查找邮箱配置
        config = EmailConfig.query.get(config_id)
        if not config:
            return jsonify({
                'success': False,
                'error': '邮箱配置不存在'
            }), 404
            
        # 简单编码存储授权码（实际项目中应使用更强的加密）
        import base64
        encoded_auth = base64.b64encode(auth_code.encode()).decode()
        
        config.password = encoded_auth
        config.description = config.description or ""
        if "AUTH_SAVED" not in config.description:
            config.description += " [AUTH_SAVED]"
        
        db.session.commit()
        
        current_app.logger.info(f"授权码已保存，配置ID: {config_id}")
        return jsonify({
            'success': True,
            'message': '授权码保存成功'
        })
        
    except Exception as e:
        current_app.logger.error(f"保存授权码失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'保存失败: {str(e)}'
        }), 500

@orders_bp.route('/email-configs/<int:config_id>/get-auth', methods=['GET'])
def get_email_auth_code(config_id):
    """获取保存的授权码"""
    try:
        config = EmailConfig.query.get(config_id)
        if not config:
            return jsonify({
                'success': False,
                'error': '邮箱配置不存在'
            }), 404
            
        # 检查是否有保存的授权码标记
        has_saved_auth = False
        if hasattr(config, 'description') and config.description:
            has_saved_auth = "AUTH_SAVED" in config.description
        
        # 如果没有标记但有password，尝试解码测试
        if not has_saved_auth and config.password:
            try:
                import base64
                base64.b64decode(config.password.encode()).decode()
                has_saved_auth = True  # 解码成功，可能是保存的授权码
            except:
                pass
        
        if not has_saved_auth:
            return jsonify({
                'success': False,
                'error': '未找到保存的授权码'
            }), 404
            
        # 解码授权码
        import base64
        try:
            decoded_auth = base64.b64decode(config.password.encode()).decode()
            return jsonify({
                'success': True,
                'auth_code': decoded_auth
            })
        except Exception:
            return jsonify({
                'success': False,
                'error': '授权码解码失败'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"获取授权码失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'获取失败: {str(e)}'
        }), 500

# 添加资源清理和健康检查功能
@orders_bp.route('/processing/health', methods=['GET'])
def check_processing_health():
    """检查处理系统健康状态"""
    try:
        # 检查活跃任务数量
        active_tasks = 0
        stalled_tasks = 0
        with task_lock:
            for task_id, task in list(processing_tasks.items()):
                # 清理过期任务（超过24小时）
                if task.start_time and (datetime.now() - task.start_time).total_seconds() > 86400:
                    if task.status == 'running':
                        task.status = 'failed'
                        task.error_message = '任务超时（24小时）'
                        task.end_time = datetime.now()
                    
                # 统计活跃和卡住的任务
                if task.status == 'running':
                    active_tasks += 1
                    # 检查是否卡住（超过1小时无进度更新）
                    if task.last_update and (datetime.now() - task.last_update).total_seconds() > 3600:
                        stalled_tasks += 1
        
        # 检查数据库连接
        db_status = "正常"
        try:
            db.session.execute("SELECT 1")
        except Exception as e:
            db_status = f"异常: {str(e)}"
        
        # 检查文件系统
        fs_status = "正常"
        try:
            test_file = "downloads/test_write.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
        except Exception as e:
            fs_status = f"异常: {str(e)}"
        
        return jsonify({
            'success': True,
            'status': 'healthy' if active_tasks < 5 and stalled_tasks == 0 and db_status == "正常" and fs_status == "正常" else 'warning',
            'tasks': {
                'active': active_tasks,
                'stalled': stalled_tasks,
                'total': len(processing_tasks)
            },
            'database': db_status,
            'filesystem': fs_status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'status': 'error',
            'error': str(e)
        }), 500

# 添加任务清理端点
@orders_bp.route('/processing/cleanup', methods=['POST'])
def cleanup_processing_tasks():
    """清理卡住或过期的处理任务"""
    try:
        data = request.get_json() or {}
        task_id = data.get('task_id')
        
        cleaned = 0
        with task_lock:
            if task_id:
                # 清理特定任务
                if task_id in processing_tasks:
                    task = processing_tasks[task_id]
                    if task.status == 'running':
                        task.status = 'cancelled'
                        task.end_time = datetime.now()
                    cleaned = 1
            else:
                # 清理所有卡住的任务
                for task_id, task in list(processing_tasks.items()):
                    if task.status == 'running':
                        # 检查是否卡住（超过1小时无进度更新）
                        if task.last_update and (datetime.now() - task.last_update).total_seconds() > 3600:
                            task.status = 'cancelled'
                            task.error_message = '任务已卡住，系统自动取消'
                            task.end_time = datetime.now()
                            cleaned += 1
        
        return jsonify({
            'success': True,
            'cleaned': cleaned
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
