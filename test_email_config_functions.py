#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱配置功能测试脚本
测试步骤1中所有邮箱配置相关的功能
"""

import requests
import json
import time
from datetime import datetime

class EmailConfigFunctionTest:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.logged_in = False
        
    def log_test(self, test_name, status, message="", details=None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⏭️"
        print(f"{status_icon} {test_name}: {message}")
        
    def login(self, username="admin", password="admin"):
        """登录系统"""
        try:
            login_page = self.session.get(f"{self.base_url}/auth/login")
            if login_page.status_code != 200:
                return False
                
            login_data = {'username': username, 'password': password}
            response = self.session.post(f"{self.base_url}/auth/login", 
                                       data=login_data, allow_redirects=True)
            
            test_response = self.session.get(f"{self.base_url}/orders/semi-auto")
            if test_response.status_code == 200 and "手动导入订单" in test_response.text:
                self.logged_in = True
                return True
            return False
        except:
            return False
    
    def test_get_email_configs(self):
        """测试获取邮箱配置列表"""
        print("\n🔍 测试获取邮箱配置列表...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/email_configs")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    configs = data.get('data', [])
                    self.log_test("获取邮箱配置列表", "PASS", 
                                f"成功获取 {len(configs)} 个邮箱配置")
                    return configs
                else:
                    self.log_test("获取邮箱配置列表", "FAIL", 
                                f"API返回错误: {data.get('message', '未知错误')}")
            else:
                self.log_test("获取邮箱配置列表", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("获取邮箱配置列表", "FAIL", f"请求异常: {str(e)}")
        
        return []
    
    def test_create_email_config(self):
        """测试创建邮箱配置"""
        print("\n🔍 测试创建邮箱配置...")
        
        # 测试用的邮箱配置数据
        test_config = {
            "name": f"测试邮箱_{int(time.time())}",
            "email": f"test_{int(time.time())}@example.com",
            "server": "imap.example.com",
            "port": 993,
            "password": "test_password",
            "download_path": "/tmp/test_downloads",
            "subject_keywords": "宜欣,生产订单",
            "enabled": True,
            "use_ssl": True
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/email_configs",
                                       json=test_config,
                                       headers={'Content-Type': 'application/json'})

            print(f"   创建响应状态码: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")

            if response.status_code in [200, 201]:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    if data.get('status') == 'success':
                        config_id = data.get('data', {}).get('id')
                        self.log_test("创建邮箱配置", "PASS",
                                    f"成功创建邮箱配置，ID: {config_id}")
                        return config_id
                    else:
                        self.log_test("创建邮箱配置", "FAIL",
                                    f"创建失败: {data.get('message', '未知错误')}")
                except:
                    self.log_test("创建邮箱配置", "FAIL", "响应不是有效的JSON")
            else:
                self.log_test("创建邮箱配置", "FAIL",
                            f"HTTP错误: {response.status_code}")

        except Exception as e:
            self.log_test("创建邮箱配置", "FAIL", f"请求异常: {str(e)}")
        
        return None
    
    def test_update_email_config(self, config_id):
        """测试更新邮箱配置"""
        print(f"\n🔍 测试更新邮箱配置 (ID: {config_id})...")
        
        if not config_id:
            self.log_test("更新邮箱配置", "SKIP", "没有可用的配置ID")
            return False
        
        # 更新数据
        update_data = {
            "name": f"更新测试邮箱_{int(time.time())}",
            "email": f"updated_test_{int(time.time())}@example.com",
            "server": "imap.updated.com",
            "port": 993,
            "download_path": "/tmp/updated_downloads",
            "subject_keywords": "宜欣,生产订单,更新",
            "enabled": True,
            "use_ssl": True
        }
        
        try:
            response = self.session.put(f"{self.base_url}/api/email_configs/{config_id}",
                                      json=update_data,
                                      headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.log_test("更新邮箱配置", "PASS", "成功更新邮箱配置")
                    return True
                else:
                    self.log_test("更新邮箱配置", "FAIL", 
                                f"更新失败: {data.get('message', '未知错误')}")
            else:
                self.log_test("更新邮箱配置", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("更新邮箱配置", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_test_email_connection(self, config_id):
        """测试邮箱连接测试功能"""
        print(f"\n🔍 测试邮箱连接测试 (ID: {config_id})...")
        
        if not config_id:
            self.log_test("邮箱连接测试", "SKIP", "没有可用的配置ID")
            return False
        
        try:
            response = self.session.post(f"{self.base_url}/api/email_configs/{config_id}/test",
                                       headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                data = response.json()
                # 连接测试可能失败（因为是测试数据），但API应该正常响应
                self.log_test("邮箱连接测试", "PASS", 
                            f"连接测试API正常响应: {data.get('message', '无消息')}")
                return True
            else:
                self.log_test("邮箱连接测试", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("邮箱连接测试", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_delete_email_config(self, config_id):
        """测试删除邮箱配置"""
        print(f"\n🔍 测试删除邮箱配置 (ID: {config_id})...")
        
        if not config_id:
            self.log_test("删除邮箱配置", "SKIP", "没有可用的配置ID")
            return False
        
        try:
            response = self.session.delete(f"{self.base_url}/api/email_configs/{config_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.log_test("删除邮箱配置", "PASS", "成功删除邮箱配置")
                    return True
                else:
                    self.log_test("删除邮箱配置", "FAIL", 
                                f"删除失败: {data.get('message', '未知错误')}")
            else:
                self.log_test("删除邮箱配置", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("删除邮箱配置", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def run_all_tests(self):
        """运行所有邮箱配置功能测试"""
        print("🚀 开始邮箱配置功能测试...")
        print("=" * 60)
        
        # 登录
        if not self.login():
            print("❌ 无法登录系统")
            return
        
        # 获取现有配置
        existing_configs = self.test_get_email_configs()
        
        # 创建测试配置
        config_id = self.test_create_email_config()
        
        # 更新配置
        self.test_update_email_config(config_id)
        
        # 测试连接
        self.test_test_email_connection(config_id)
        
        # 删除测试配置
        self.test_delete_email_config(config_id)
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 邮箱配置功能测试结果")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        skipped_tests = len([r for r in self.test_results if r['status'] == 'SKIP'])
        
        print(f"总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"⏭️ 跳过: {skipped_tests}")
        
        if total_tests > 0:
            print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  - {result['test_name']}: {result['message']}")

if __name__ == "__main__":
    test_suite = EmailConfigFunctionTest()
    test_suite.run_all_tests()
