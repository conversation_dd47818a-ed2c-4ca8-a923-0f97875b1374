{% extends "base.html" %}

{% set page_title = "手动导入订单" %}

{% block title %}{{ page_title }} - APS智能调度平台{% endblock %}

{% block extra_css %}
<style>
/* 订单处理控制台专用样式 */
.order-processing-console {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    min-height: 100vh;
}

/* 三步骤工作流导航 */
.workflow-steps {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 2px solid var(--gray-200);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--gray-800);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.workflow-steps h5 {
    color: var(--theme-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.steps-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.step-item {
    flex: 1;
    text-align: center;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 1rem;
    border-radius: 0.375rem;
    background: white;
    border: 2px solid var(--gray-200);
    color: var(--gray-700);
}

.step-item:hover {
    border-color: var(--theme-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(183, 36, 36, 0.15);
}

.step-item.step-active {
    background: var(--theme-color);
    border-color: var(--theme-color);
    color: white;
    box-shadow: 0 4px 16px rgba(183, 36, 36, 0.3);
}

.step-item.step-completed {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.step-number {
    display: inline-block;
    width: 2.5rem;
    height: 2.5rem;
    line-height: 2.5rem;
    border-radius: 50%;
    background: var(--gray-200);
    color: var(--gray-600);
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step-active .step-number {
    background: white;
    color: var(--theme-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-completed .step-number {
    background: white;
    color: var(--success-color);
}

.step-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.step-description {
    font-size: 0.75rem;
    opacity: 0.9;
    line-height: 1.3;
}

.step-connector {
    width: 2rem;
    height: 2px;
    background: var(--gray-300);
    position: relative;
    margin: 0 0.5rem;
}

.step-connector.completed {
    background: var(--success-color);
}

/* 紧凑工作流样式 */
.workflow-steps-compact {
    padding: 0;
}

.steps-container-compact {
    align-items: center;
    gap: 15px;
}

.step-item-compact {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(108, 117, 125, 0.1);
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.step-item-compact:hover {
    background: rgba(108, 117, 125, 0.15);
    transform: translateY(-1px);
}

.step-item-compact.step-active {
    background: rgba(13, 110, 253, 0.1);
    border-color: #0d6efd;
    color: #0d6efd;
}

.step-item-compact.step-completed {
    background: rgba(25, 135, 84, 0.1);
    border-color: #198754;
    color: #198754;
}

.step-number-compact {
    background: #6c757d;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
    flex-shrink: 0;
}

.step-active .step-number-compact {
    background: #0d6efd;
}

.step-completed .step-number-compact {
    background: #198754;
}

.step-title-compact {
    font-weight: 500;
    font-size: 14px;
    white-space: nowrap;
}

.step-connector-compact {
    width: 20px;
    height: 2px;
    background: rgba(108, 117, 125, 0.3);
    flex-shrink: 0;
}

/* 工作区域面板 */
.work-area {
    margin-bottom: 1.5rem;
}

.step-panel {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.step-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 监控仪表板 */
.progress-monitor {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    transition: box-shadow 0.3s ease;
}

.progress-monitor:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-monitor .card-header {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
}

.progress-monitor .progress {
    height: 0.75rem;
    border-radius: 0.375rem;
}

.progress-monitor .progress-bar {
    background: linear-gradient(90deg, var(--theme-color), #d73027);
    transition: width 0.5s ease;
}

/* 渐变背景样式 */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--theme-color), #d73027) !important;
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, #6c757d, #495057) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
}

.bg-gradient-dark {
    background: linear-gradient(135deg, #343a40, #212529) !important;
}

.bg-gradient-success {
    background: linear-gradient(90deg, #28a745, #20c997) !important;
}

/* 任务控制台 */
.task-control-panel {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    transition: box-shadow 0.3s ease;
}

.task-control-panel:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.task-controls .btn {
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

/* 卡片悬停效果 */
.card.shadow-sm:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* 状态指示器 */
.status-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 日志终端样式 */
.log-content {
    font-size: 0.875rem;
    line-height: 1.4;
}

.log-entry {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background-color 0.2s ease;
}

.log-entry:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.log-entry.log-error {
    border-left: 3px solid #dc3545;
}

.log-entry.log-warning {
    border-left: 3px solid #ffc107;
}

.log-entry.log-success {
    border-left: 3px solid #28a745;
}

.log-entry.log-info {
    border-left: 3px solid #17a2b8;
}

.log-timestamp {
    font-weight: 600;
    margin-right: 0.5rem;
}

/* 快速统计数字动画 */
.h6 {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式布局优化 */
@media (max-width: 992px) {
    .task-controls .d-grid .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .log-content {
        height: 250px !important;
    }
}

@media (max-width: 768px) {
    .task-controls .d-grid {
        gap: 0.5rem !important;
    }
    
    .task-controls .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
    
    .log-content {
        height: 200px !important;
    }
    
    .card-header h6 {
        font-size: 0.9rem;
    }
}

.task-controls .btn:hover {
    transform: translateY(-1px);
}

.task-controls .btn-success:hover {
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.task-controls .btn-primary:hover {
    box-shadow: 0 4px 8px rgba(183, 36, 36, 0.3);
}

.task-controls .btn-warning:hover {
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.task-controls .btn-danger:hover {
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
    white-space: nowrap;
}

.status-indicator.status-idle {
    background: var(--gray-200);
    color: var(--gray-600);
}

.status-indicator.status-running {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.status-indicator.status-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-indicator.status-error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.connection-status {
    display: inline-flex;
    align-items: center;
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 1rem;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: rgba(40, 167, 69, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.connection-status.disconnected {
    background: rgba(220, 53, 69, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.connection-status.connecting {
    background: rgba(255, 193, 7, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

/* 日志查看器 */
.log-viewer {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    height: 400px;
    display: flex;
    flex-direction: column;
    transition: box-shadow 0.3s ease;
}

.log-viewer:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--gray-300);
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 0.5rem 0.5rem 0 0;
}

.log-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.log-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 0.8rem;
    line-height: 1.4;
    background: #1e1e1e;
    color: #d4d4d4;
    border-radius: 0 0 0.5rem 0.5rem;
}

.log-entry {
    margin-bottom: 0.25rem;
    padding: 0.25rem 0;
    border-left: 3px solid transparent;
    padding-left: 0.5rem;
}

.log-entry.log-info {
    border-left-color: var(--info-color);
    color: #9cdcfe;
}

.log-entry.log-success {
    border-left-color: var(--success-color);
    color: #4ec9b0;
}

.log-entry.log-warning {
    border-left-color: var(--warning-color);
    color: #ffcc02;
}

.log-entry.log-error {
    border-left-color: var(--danger-color);
    color: #f48771;
}

.log-timestamp {
    color: #808080;
    margin-right: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .steps-container {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .step-connector {
        display: none;
    }
    
    .task-controls {
        flex-wrap: wrap;
    }
    
    .log-viewer {
        height: 300px;
    }
}

/* 动画效果 */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.rotate {
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 步骤3专用样式 */
.order-info-box {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.order-info-box h6 {
    color: #d21919;
    margin-bottom: 8px;
}

.quick-filters .btn-xs {
    padding: 2px 8px;
    font-size: 0.75rem;
}

.order-table-container {
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.order-table-container .table th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 10;
    white-space: nowrap;
    font-size: 0.875rem;
    font-weight: 600;
    padding: 8px 6px;
}

.order-table-container .table td {
    white-space: nowrap;
    font-size: 0.875rem;
    padding: 6px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.select-column {
    width: 35px;
    min-width: 35px;
    max-width: 35px;
    text-align: center;
}

.action-column {
    width: 120px;
    min-width: 120px;
    max-width: 120px;
}

/* 🔧 修复问题1：数据类型行横向扩展 */
.data-type-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.data-type-container > div:first-child {
    min-width: 200px;
    margin-right: 2rem;
}

.statistics-row {
    flex: 1;
    min-width: 400px;
}

/* 🔧 修复问题2：orderLoadingOverlay层级和穿透 */
.loading-overlay {
    position: absolute !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050 !important; /* 确保在其他元素之上 */
    border-radius: 0.375rem;
    backdrop-filter: blur(2px);
}

.order-table-container {
    position: relative;
    z-index: 1;
    isolation: isolate; /* 创建新的层叠上下文 */
}

/* 确保任务进度和控制面板正常布局 */
.row.mb-3 {
    position: static !important;
    z-index: auto !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.card.shadow-sm {
    position: static !important;
    z-index: auto !important;
    width: 100% !important;
    max-width: none !important;
}

/* 紧凑布局样式 */
.card.h-100 {
    min-height: 140px !important;
    max-height: 140px !important;
}

.card.h-100 .card-body {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
}

.btn-group .btn {
    font-size: 0.8rem !important;
    padding: 0.25rem 0.5rem !important;
}

.progress {
    border-radius: 3px !important;
}

.form-check-input {
    margin-top: 0.1rem !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card.h-100 {
        min-height: auto !important;
        max-height: none !important;
    }
}

/* 🔧 修复问题3：更新表格样式以适应更多字段 */
.order-table-container .table {
    font-size: 0.8rem; /* 缩小字体以容纳更多字段 */
}

.order-table-container .table th,
.order-table-container .table td {
    padding: 4px 6px; /* 减小内边距 */
    max-width: 120px; /* 限制最大宽度 */
    min-width: 80px; /* 设置最小宽度 */
}

/* 特定字段的宽度调整 */
.order-table-container .table th:nth-child(2),
.order-table-container .table td:nth-child(2) {
    min-width: 120px; /* 订单号 */
}

.order-table-container .table th:nth-child(3),
.order-table-container .table td:nth-child(3) {
    min-width: 100px; /* 日期 */
}

.order-table-container .table th:nth-child(4),
.order-table-container .table td:nth-child(4) {
    min-width: 150px; /* 标签名称 */
}

/* 响应式表格 */
@media (max-width: 1200px) {
    .order-table-container .table {
        font-size: 0.75rem;
    }
    
    .order-table-container .table th,
    .order-table-container .table td {
        padding: 3px 4px;
        max-width: 100px;
    }
}

/* 数据类型切换响应式 */
@media (max-width: 768px) {
    .data-type-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .data-type-container > div:first-child {
        margin-right: 0;
        width: 100%;
    }
    
    .statistics-row {
        width: 100%;
        min-width: auto;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="order-processing-console">
    <div class="container-fluid">
        <!-- 页面标题栏 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">{{ page_title }}</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                                <li class="breadcrumb-item">订单管理</li>
                                <li class="breadcrumb-item active" aria-current="page">订单处理中心</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="connection-status" id="connectionStatus">
                            <i class="fas fa-circle pulse"></i>
                            连接中...
                        </span>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetWorkflow()">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 紧凑的工作流和控制面板 -->
        <div class="row mb-3">
            <!-- 三步骤工作流导航 - 压缩版 -->
            <div class="col-lg-8 col-md-12 mb-2">
                <div class="card shadow-sm">
                    <div class="card-body py-2 px-3">
                        <div class="workflow-steps-compact">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-route me-2"></i>订单处理工作流</h6>
                                <div class="steps-container-compact d-flex">
                                    <div class="step-item-compact step-active" data-step="1" onclick="switchStep(1)">
                                        <div class="step-number-compact">1</div>
                                        <div class="step-title-compact">邮箱配置</div>
                                    </div>
                                    <div class="step-connector-compact"></div>
                                    <div class="step-item-compact" data-step="2" onclick="switchStep(2)">
                                        <div class="step-number-compact">2</div>
                                        <div class="step-title-compact">附件解析</div>
                                    </div>
                                    <div class="step-connector-compact"></div>
                                    <div class="step-item-compact" data-step="3" onclick="switchStep(3)">
                                        <div class="step-number-compact">3</div>
                                        <div class="step-title-compact">数据管理</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务控制面板 - 压缩版 -->
            <div class="col-lg-4 col-md-12 mb-2">
                <div class="card shadow-sm">
                    <div class="card-body py-2 px-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0"><i class="fas fa-cogs me-1"></i>任务控制</h6>
                            <span class="status-indicator badge bg-light text-dark small" id="taskStatus">
                                <i class="fas fa-circle me-1"></i>待命中
                            </span>
                        </div>
                        <div class="btn-group w-100 mb-2" role="group">
                            <button type="button" class="btn btn-success btn-sm" id="startBtn" onclick="startProcessing('auto')">
                                <i class="fas fa-play me-1"></i>自动
                            </button>
                            <button type="button" class="btn btn-primary btn-sm" id="stepBtn" onclick="startProcessing('step')">
                                <i class="fas fa-step-forward me-1"></i>分步
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" id="pauseBtn" onclick="pauseProcessing()" disabled>
                                <i class="fas fa-pause me-1"></i>暂停
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" id="stopBtn" onclick="stopProcessing()" disabled>
                                <i class="fas fa-stop me-1"></i>停止
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务进度和实时日志 -->
        <div class="row mb-3">
            <!-- 任务进度监控 -->
            <div class="col-lg-6 col-md-6 mb-2">
                <div class="card shadow-sm">
                    <div class="card-header bg-gradient-primary text-white py-2">
                        <h6 class="mb-0 small"><i class="fas fa-tachometer-alt me-1"></i>任务进度</h6>
                    </div>
                    <div class="card-body py-2 px-3">
                        <div class="row g-2 mb-2">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="small text-muted mb-1">总体</div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-gradient-success" id="overallProgressBar" style="width: 0%" role="progressbar"></div>
                                    </div>
                                    <div class="small fw-bold mt-1" id="overallProgress">0%</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="small text-muted mb-1">步骤</div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-gradient-info" id="stepProgressBar" style="width: 0%" role="progressbar"></div>
                                    </div>
                                    <div class="small fw-bold mt-1" id="stepProgress">0%</div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <small id="progressMessage" class="text-muted" style="font-size: 0.75rem;">待命中</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时日志查看器 -->
            <div class="col-lg-6 col-md-6 mb-2">
                <div class="card shadow-sm">
                    <div class="card-header bg-gradient-info text-white py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0 small"><i class="fas fa-terminal me-1"></i>实时日志</h6>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="clearLogs()">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="toggleLogAutoScroll()" id="autoScrollBtn">
                                    <i class="fas fa-arrow-down"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="log-content" id="logContent" style="height: 120px; overflow-y: auto; padding: 8px; background: #f8f9fa; font-family: 'Courier New', monospace; font-size: 0.75rem;">
                            <div class="log-entry text-muted">
                                <span class="log-timestamp">[系统]</span>
                                <span class="log-message">日志查看器已初始化，等待消息...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要工作区域 -->
        <div class="row work-area">
            <div class="col">
                <!-- 步骤1：邮箱配置管理面板 -->
                <div class="step-panel active" id="step1Panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-envelope me-2"></i>邮箱配置管理</h6>
                                <button type="button" class="btn btn-success btn-sm" onclick="showAddEmailModal()">
                                    <i class="fas fa-plus me-1"></i>新增邮箱
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">
                                配置Outlook邮箱连接，设置自动获取"宜欣 生产订单"相关附件的规则。
                                <span class="badge bg-primary ms-2">支持IMAP/SMTP</span>
                            </p>
                            
                            <!-- 邮箱配置列表 -->
                            <div class="row mb-4">
                                <div class="col">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">已配置邮箱 <span class="badge bg-secondary" id="emailConfigCount">0</span></h6>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshEmailConfigs()">
                                            <i class="fas fa-sync-alt me-1"></i>刷新
                                        </button>
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th width="25%">邮箱地址</th>
                                                    <th width="15%">服务器</th>
                                                    <th width="20%">关键词筛选</th>
                                                    <th width="15%">状态</th>
                                                    <th width="15%">最后同步</th>
                                                    <th width="10%">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="emailConfigTableBody">
                                                <tr id="noEmailConfigRow">
                                                    <td colspan="6" class="text-center text-muted py-4">
                                                        <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                                                        暂无邮箱配置，请点击"新增邮箱"开始配置
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 测试连接区域 -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0"><i class="fas fa-plug me-2"></i>连接测试</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <select class="form-select form-select-sm" id="testEmailSelect">
                                                    <option value="">选择要测试的邮箱配置</option>
                                                </select>
                                            </div>
                                            <button type="button" class="btn btn-info btn-sm" onclick="testEmailConnection()" id="testConnectionBtn">
                                                <i class="fas fa-play me-1"></i>测试连接
                                            </button>
                                            <div class="mt-2" id="testResult"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0"><i class="fas fa-download me-2"></i>附件预览</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <select class="form-select form-select-sm" id="previewEmailSelect">
                                                    <option value="">选择要预览的邮箱配置</option>
                                                </select>
                                            </div>
                                            <button type="button" class="btn btn-success btn-sm" onclick="previewAttachments()" id="previewBtn">
                                                <i class="fas fa-eye me-1"></i>预览附件
                                            </button>
                                            <div class="mt-2" id="previewResult"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 步骤2：附件获取与解析面板 -->
                <div class="step-panel" id="step2Panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-file-excel me-2"></i>附件获取与解析</h6>
                                <div>
                                    <button type="button" class="btn btn-primary btn-sm me-2" onclick="startAttachmentProcessing()">
                                        <i class="fas fa-play me-1"></i>开始处理
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshAttachmentsList()">
                                        <i class="fas fa-sync-alt me-1"></i>刷新
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">
                                自动扫描配置的邮箱，下载相关Excel附件并进行智能解析。
                                <span class="badge bg-success ms-2">支持横向/纵向信息提取</span>
                            </p>
                            
                            <!-- 处理状态概览 -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-light border-0">
                                        <div class="card-body text-center p-3">
                                            <div class="h4 mb-1 text-primary" id="totalAttachmentsCount">--</div>
                                            <div class="small text-muted">检测到附件</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light border-0">
                                        <div class="card-body text-center p-3">
                                            <div class="h4 mb-1 text-success" id="processedCount">--</div>
                                            <div class="small text-muted">已处理</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light border-0">
                                        <div class="card-body text-center p-3">
                                            <div class="h4 mb-1 text-warning" id="pendingCount">--</div>
                                            <div class="small text-muted">待处理</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-light border-0">
                                        <div class="card-body text-center p-3">
                                            <div class="h4 mb-1 text-danger" id="errorCount">--</div>
                                            <div class="small text-muted">处理失败</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 附件列表和处理详情 -->
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <i class="fas fa-file-excel me-2"></i>附件列表
                                                <span class="badge bg-secondary ms-2" id="attachmentListCount">0</span>
                                            </h6>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-info" onclick="scanAttachments()" id="scanAttachmentsBtn">
                                                        <i class="fas fa-envelope me-1"></i>扫描邮箱
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary" onclick="processSelectedAttachments()" id="processSelectedBtn" disabled>
                                                        <i class="fas fa-play me-1"></i>处理选中
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success" onclick="processBatchAttachments()" id="processBatchBtn">
                                                        <i class="fas fa-magic me-1"></i>批量处理
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body p-0">
                                            <div class="table-responsive" style="max-height: 400px;">
                                                <table class="table table-sm table-hover mb-0">
                                                    <thead class="table-light sticky-top">
                                                        <tr>
                                                            <th style="width: 5%">
                                                                <input type="checkbox" id="selectAllAttachments" onchange="toggleAllAttachments()">
                                                            </th>
                                                            <th style="width: 30%">文件名</th>
                                                            <th style="width: 10%">大小</th>
                                                            <th style="width: 15%">接收时间</th>
                                                            <th style="width: 10%">文件夹</th>
                                                            <th style="width: 15%">状态</th>
                                                            <th style="width: 15%">操作</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="attachmentTableBody">
                                                        <tr id="noAttachmentsRow">
                                                            <td colspan="7" class="text-center text-muted py-4">
                                                                <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                                                                暂无附件，请点击"扫描"按钮查找Excel文件
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">选中 <span id="selectedAttachmentsCount">0</span> 个附件</small>
                                                <div>
                                                    <button type="button" class="btn btn-success btn-sm" onclick="processSelectedAttachments()">
                                                        <i class="fas fa-cogs me-1"></i>处理选中
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteSelectedAttachments()">
                                                        <i class="fas fa-trash me-1"></i>删除选中
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <!-- 扫描配置 -->
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-search me-2"></i>扫描配置</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="emailConfigSelect" class="form-label">邮箱配置</label>
                                                <select class="form-select form-select-sm" id="emailConfigSelect">
                                                    <option value="">选择邮箱配置...</option>
                                                </select>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="emailScanDays" class="form-label">扫描天数</label>
                                                <input type="number" class="form-control form-control-sm" id="emailScanDays" value="7" min="1" max="30">
                                                <div class="form-text">扫描最近多少天的邮件</div>
                                            </div>
                                            
                                            <div class="d-grid">
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="scanAttachments()">
                                                    <i class="fas fa-envelope me-1"></i>扫描邮箱附件
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 步骤3：现代化订单数据管理面板 -->
                <div class="step-panel" id="step3Panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>订单数据管理</h6>
                                <div>
                                    <button type="button" class="btn btn-success btn-sm me-2" onclick="exportSelectedOrderData()">
                                        <i class="fas fa-download me-1"></i>导出数据
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshOrderData()">
                                        <i class="fas fa-sync-alt me-1"></i>刷新
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <!-- 说明区域 -->
                            <div class="order-info-box">
                                <h6><i class="fas fa-info-circle me-1"></i>订单数据管理</h6>
                                                <div class="flex-grow-1 ms-4">
                                                    <div class="row text-center">
                                                        <div class="col-3">
                                                            <div class="h4 mb-1 text-primary" id="totalOrderRecords">--</div>
                                                            <div class="small text-muted">总记录</div>
                                                        </div>
                                                        <div class="col-3">
                                                            <div class="h4 mb-1 text-success" id="validOrderRecords">--</div>
                                                            <div class="small text-muted">有效记录</div>
                                                        </div>
                                                        <div class="col-3">
                                                            <div class="h4 mb-1 text-warning" id="engineeringOrderCount">--</div>
                                                            <div class="small text-muted">工程订单</div>
                                                        </div>
                                                        <div class="col-3">
                                                            <div class="h4 mb-1 text-info" id="productionOrderCount">--</div>
                                                            <div class="small text-muted">量产订单</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <h6 class="mb-0">数据类型</h6>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <input type="radio" class="btn-check" name="orderType" id="ftOrders" value="ft_summary" checked onchange="switchOrderType('ft_summary')">
                                                    <label class="btn btn-outline-success" for="ftOrders">FT订单</label>
                                                    
                                                    <input type="radio" class="btn-check" name="orderType" id="cpOrders" value="cp_summary" onchange="switchOrderType('cp_summary')">
                                                    <label class="btn btn-outline-primary" for="cpOrders">CP订单</label>
                                                </div>
                                            </div>
                                            
                                            <div class="row text-center">
                                                <div class="col-3">
                                                    <div class="h4 mb-1 text-primary" id="totalOrderRecords">--</div>
                                                    <div class="small text-muted">总记录</div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="h4 mb-1 text-success" id="validOrderRecords">--</div>
                                                    <div class="small text-muted">有效记录</div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="h4 mb-1 text-warning" id="engineeringOrderCount">--</div>
                                                    <div class="small text-muted">工程订单</div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="h4 mb-1 text-info" id="productionOrderCount">--</div>
                                                    <div class="small text-muted">量产订单</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                
                            <!-- 高级筛选面板 -->
                            <div class="mb-3">
                                <h6>
                                    <i class="fas fa-filter me-2"></i>高级筛选
                                    <small class="text-muted ms-2">支持多条件组合查询</small>
                                </h6>
                                
                                <!-- 快速筛选按钮 -->
                                <div class="quick-filters mb-3">
                                    <small class="text-muted me-2">快速筛选：</small>
                                    <button type="button" class="btn btn-outline-primary btn-xs me-1" onclick="quickOrderFilter('classification_result', '量产')">
                                        量产订单
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-xs me-1" onclick="quickOrderFilter('classification_result', '工程')">
                                        工程订单
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-xs me-1" onclick="quickOrderFilter('order_date', 'today')">
                                        今日订单
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-xs me-1" onclick="quickOrderFilter('delivery_date', 'urgent')">
                                        紧急订单
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-xs" onclick="clearOrderFilter()">
                                        清除筛选
                                    </button>
                                </div>
                                
                                <!-- 高级筛选条件 -->
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label form-label-sm">字段</label>
                                        <select class="form-select form-select-sm" id="orderFilterField">
                                            <option value="">选择字段</option>
                                            <option value="order_number">订单号</option>
                                            <option value="label_name">标签名称</option>
                                            <option value="chip_name">芯片名称</option>
                                            <option value="classification_result">分类结果</option>
                                            <option value="lot_type1">Lot Type</option>
                                            <option value="order_date">下单日期</option>
                                            <option value="delivery_date">交期</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label form-label-sm">操作符</label>
                                        <select class="form-select form-select-sm" id="orderFilterOperator">
                                            <option value="contains">包含</option>
                                            <option value="equals">等于</option>
                                            <option value="starts_with">开始于</option>
                                            <option value="ends_with">结束于</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label form-label-sm">值</label>
                                        <input type="text" class="form-control form-control-sm" id="orderFilterValue" placeholder="输入筛选值">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label form-label-sm">&nbsp;</label>
                                        <div>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="applyOrderFilter()">
                                                <i class="fas fa-search me-1"></i>筛选
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label form-label-sm">&nbsp;</label>
                                        <div>
                                            <select class="form-select form-select-sm" id="orderPageSize" onchange="changeOrderPageSize()">
                                                <option value="25">25 条/页</option>
                                                <option value="50" selected>50 条/页</option>
                                                <option value="100">100 条/页</option>
                                                <option value="all">显示全部</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 批量操作工具栏 -->
                            <div class="batch-operations mb-3" style="display: none;" id="orderBatchOperations">
                                <div class="alert alert-info py-2">
                                    <span id="orderSelectedCount">0</span> 条记录已选择
                                    <button type="button" class="btn btn-sm btn-outline-success ms-3" onclick="batchExportOrders()">
                                        <i class="fas fa-file-excel me-1"></i>批量导出
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning ms-2" onclick="batchEditOrders()">
                                        <i class="fas fa-edit me-1"></i>批量编辑
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="batchDeleteOrders()">
                                        <i class="fas fa-trash me-1"></i>批量删除
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearOrderSelection()">
                                        <i class="fas fa-times me-1"></i>取消选择
                                    </button>
                                </div>
                            </div>

                            <!-- 数据表格 -->
                            <div class="order-table-container" style="position: relative; max-height: 65vh;">
                                <div class="loading-overlay" id="orderLoadingOverlay" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-sm table-hover table-striped" id="orderDataTable">
                                        <thead class="table-light sticky-top">
                                            <tr id="orderTableHeaders">
                                                <th class="select-column">
                                                    <input type="checkbox" id="selectAllOrders" onchange="toggleAllOrderRecords()">
                                                </th>
                                                <!-- 表头将根据数据类型动态生成 -->
                                            </tr>
                                        </thead>
                                        <tbody id="orderTableBody">
                                            <tr>
                                                <td colspan="20" class="text-center py-4">
                                                    <i class="fas fa-database fa-2x mb-2 d-block text-muted"></i>
                                                    <span class="text-muted">暂无数据，请先处理Excel附件</span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- 分页和记录信息 -->
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="order-pagination-info">
                                    <small class="text-muted">
                                        显示第 <span id="orderCurrentStart">0</span> - <span id="orderCurrentEnd">0</span> 条，
                                        共 <span id="orderTotalRecords">0</span> 条记录
                                        <span class="badge bg-secondary ms-2" id="filteredOrderCount">未筛选</span>
                                    </small>
                                </div>
                                <nav aria-label="订单数据分页">
                                    <ul class="pagination pagination-sm mb-0" id="orderPagination">
                                        <!-- 分页按钮将动态生成 -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


                    </div>
                            </div>
        </div>
    </div>
</div>

<!-- 邮箱配置模态框 -->
<div class="modal fade" id="emailConfigModal" tabindex="-1" aria-labelledby="emailConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailConfigModalLabel">
                    <i class="fas fa-envelope me-2"></i><span id="modalTitle">新增邮箱配置</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="emailConfigForm">
                    <input type="hidden" id="configId" name="configId">
                    
                    <!-- 基本信息 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="configName" class="form-label">配置名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="configName" name="configName" required>
                            <div class="form-text">例如：宜欣邮箱配置</div>
                        </div>
                        <div class="col-md-6">
                            <label for="emailAddress" class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="emailAddress" name="emailAddress" required>
                            <div class="form-text">例如：<EMAIL></div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="emailPassword" class="form-label">授权码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="emailPassword" name="emailPassword" required>
                            <div class="form-text">
                                请使用网易企业邮箱的授权码，不是登录密码
                                <a href="#" onclick="showAuthCodeHelp()" class="text-decoration-none ms-1">
                                    <i class="fas fa-question-circle"></i> 如何获取？
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 授权码帮助信息 -->
                    <div class="alert alert-info d-none" id="authCodeHelp">
                        <h6><i class="fas fa-info-circle me-2"></i>网易企业邮箱授权码获取步骤：</h6>
                        <ol class="mb-2">
                            <li>登录网易企业邮箱网页版</li>
                            <li>进入"设置" → "客户端授权密码"</li>
                            <li>开启"IMAP/SMTP服务"</li>
                            <li>生成并复制授权码</li>
                            <li>在此处填入授权码（不是登录密码）</li>
                        </ol>
                        <small class="text-muted">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            注意：授权码只显示一次，请妥善保存
                        </small>
                    </div>
                    
                    <!-- 服务器配置 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="imapServer" class="form-label">IMAP服务器 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="imapServer" name="imapServer" required placeholder="imap.qiye.163.com">
                        </div>
                        <div class="col-md-6">
                            <label for="imapPort" class="form-label">IMAP端口 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="imapPort" name="imapPort" required value="993">
                        </div>
                    </div>
                    
                    <!-- 筛选规则 -->
                    <div class="mb-3">
                        <label for="senderFilter" class="form-label">发件人筛选</label>
                        <input type="text" class="form-control" id="senderFilter" name="senderFilter" placeholder="<EMAIL>">
                        <div class="form-text">只处理来自特定发件人的邮件，多个用分号分隔，留空则不限制</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subjectKeywords" class="form-label">主题关键词 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="subjectKeywords" name="subjectKeywords" required value="宜欣;生产订单">
                        <div class="form-text">多个关键词用分号分隔，例如：宜欣;生产订单</div>
                    </div>
                    
                    <!-- 工作时间设置 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="checkInterval" class="form-label">检查频率(分钟)</label>
                            <input type="number" class="form-control" id="checkInterval" name="checkInterval" value="60" min="1" max="1440">
                        </div>
                        <div class="col-md-4">
                            <label for="workStartTime" class="form-label">工作开始时间</label>
                            <input type="time" class="form-control" id="workStartTime" name="workStartTime" value="08:00">
                        </div>
                        <div class="col-md-4">
                            <label for="workEndTime" class="form-label">工作结束时间</label>
                            <input type="time" class="form-control" id="workEndTime" name="workEndTime" value="18:00">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="fetchDays" class="form-label">抓取天数</label>
                            <input type="number" class="form-control" id="fetchDays" name="fetchDays" value="10" min="1" max="30">
                            <div class="form-text">抓取最近多少天的邮件</div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="enableConfig" name="enableConfig" checked>
                                <label class="form-check-label" for="enableConfig">启用此配置</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 高级设置 -->
                    <div class="card border-secondary mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-2"></i>高级设置
                                <button type="button" class="btn btn-link btn-sm p-0 ms-2" data-bs-toggle="collapse" data-bs-target="#advancedSettings">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </h6>
                        </div>
                        <div class="collapse" id="advancedSettings">
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <label for="downloadPath" class="form-label">附件保存路径 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="downloadPath" name="downloadPath" value="downloads/email_attachments" required>
                                        <div class="form-text">附件将保存到此目录</div>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="maxAttachmentSize" class="form-label">最大附件大小(MB)</label>
                                        <input type="number" class="form-control" id="maxAttachmentSize" name="maxAttachmentSize" value="50" min="1" max="100">
                                        <div class="form-text">超过此大小的附件将被忽略</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="allowedExtensions" class="form-label">允许的文件扩展名</label>
                                        <input type="text" class="form-control" id="allowedExtensions" name="allowedExtensions" value=".xlsx,.xls,.csv">
                                        <div class="form-text">多个扩展名用逗号分隔</div>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="useDateFolder" name="useDateFolder" checked>
                                            <label class="form-check-label" for="useDateFolder">按日期分类保存</label>
                                            <div class="form-text">在保存路径下按日期创建子文件夹</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableSSL" name="enableSSL" checked>
                                            <label class="form-check-label" for="enableSSL">启用SSL加密</label>
                                            <div class="form-text">推荐启用以确保连接安全</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="markAsRead" name="markAsRead" checked>
                                            <label class="form-check-label" for="markAsRead">标记邮件为已读</label>
                                            <div class="form-text">处理后自动标记邮件为已读状态</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" onclick="testEmailConfigInModal()">
                    <i class="fas fa-plug me-1"></i>测试连接
                </button>
                <button type="button" class="btn btn-primary" onclick="saveEmailConfig()">
                    <i class="fas fa-save me-1"></i>保存配置
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// ===== 第二阶段：核心功能集成的JavaScript实现 =====

// 全局变量和状态管理
let currentStep = 1;
let currentTaskId = null;
let websocketConnection = null;
let processingStatus = 'idle';
let emailConfigs = [];
let attachmentsList = [];
let parsedDataRecords = [];
let isProcessing = false;

// API端点配置
const API_ENDPOINTS = {
    emailConfig: '/api/email_configs',
    attachments: '/api/v2/orders/attachments',
    processing: '/api/v2/orders/processing',
    data: '/api/v2/orders/data',
    summary: '/api/order_data/stats',
    export: '/api/order_data/export'
};

// ===== 页面初始化 =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('订单处理控制台初始化...');
    initializeConsole();
    loadInitialData();
});

// 初始化控制台
function initializeConsole() {
    updateConnectionStatus('connecting');
    initializeWebSocket();
    setupEventListeners();
    
    // 初始化步骤显示
    currentStep = 1;
    switchStep(1);
    
    setTimeout(() => {
        updateConnectionStatus('connected');
        addLogEntry('success', '订单处理控制台初始化完成');
    }, 1000);
}

function setupEventListeners() {
    // 设置日期筛选器监听器
    const dateRangeFilter = document.getElementById('dateRangeFilter');
    if (dateRangeFilter) {
        dateRangeFilter.addEventListener('change', handleDateRangeChange);
    }
    
    // 设置其他事件监听器
    setupStepNavigation();
}

function setupStepNavigation() {
    // 移除已有的事件监听器，防止重复绑定
    document.querySelectorAll('.step-item').forEach((item, index) => {
        // 创建新的点击处理函数
        const clickHandler = () => switchStep(index + 1);
        
        // 移除旧的监听器（如果有）
        item.removeEventListener('click', clickHandler);
        
        // 添加新的监听器
        item.addEventListener('click', clickHandler);
    });
}

// ===== WebSocket连接管理 =====
function setupWebsocket() {
    try {
        // 检查是否已有连接
        if (websocketConnection) {
            console.log('已有Socket.IO连接，断开重连');
            websocketConnection.disconnect();
        }
        
        // 创建新连接
        websocketConnection = io();
        
        // 连接事件处理
        websocketConnection.on('connect', function() {
            console.log('Socket.IO连接成功');
            updateConnectionStatus('connected');
            addLogEntry('success', 'Socket.IO连接成功');
        });
        
        websocketConnection.on('disconnect', function() {
            console.log('Socket.IO连接断开');
            updateConnectionStatus('disconnected');
            addLogEntry('warning', 'Socket.IO连接断开，尝试重连...');
            
            // 5秒后尝试重连
            setTimeout(function() {
                if (websocketConnection.disconnected) {
                    setupWebsocket();
                }
            }, 5000);
        });
        
        // 添加任务超时处理
        let taskTimeouts = {};
        
        websocketConnection.on('task_progress', function(data) {
            console.log('收到task_progress事件:', data);
            updateTaskProgress(data);
            
            // 重置任务超时计时器
            if (taskTimeouts[data.task_id]) {
                clearTimeout(taskTimeouts[data.task_id]);
            }
            
            // 设置新的超时计时器（10分钟无更新则认为任务卡住）
            if (data.status === 'running') {
                taskTimeouts[data.task_id] = setTimeout(function() {
                    addLogEntry('error', `任务 ${data.task_id} 可能已卡住，超过10分钟无更新`);
                    updateTaskStatus('warning');
                }, 600000);
            } else {
                // 任务完成或失败，清除超时
                clearTimeout(taskTimeouts[data.task_id]);
                delete taskTimeouts[data.task_id];
            }
        });
        
        // 其他事件处理...
    } catch (error) {
        console.error('Socket.IO连接失败:', error);
        addLogEntry('error', `Socket.IO连接失败: ${error.message}`);
        updateConnectionStatus('error');
    }
}

// WebSocket消息处理已移至initializeWebSocket函数中的事件监听器中
// 保留此函数用于兼容性，但不再使用
function handleWebSocketMessage(message) {
    console.log('收到WebSocket消息:', message);
}

// ===== 数据加载和刷新 =====
function loadInitialData() {
    refreshEmailConfigs();
    // 移除自动扫描附件，改为用户手动触发
    // scanAttachments();
    refreshSummaryData();
    // 自动加载已有数据预览
    refreshDataPreview();
    updateTaskProgress({ overall: 0, step: 0, message: '待命中' });
    
    // 初始化快速统计面板
    // 实时统计功能已移除
}

function refreshEmailConfigs() {
    fetch(API_ENDPOINTS.emailConfig)
    .then(response => response.json())
    .then(data => {
        if (data.success || data.status === 'success') {
            emailConfigs = data.data || [];
            updateEmailConfigTable();
            updateEmailConfigSelects();
            addLogEntry('info', `邮箱配置已刷新，共${emailConfigs.length}个配置`);
        } else {
            addLogEntry('error', '获取邮箱配置失败：' + (data.error || data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '刷新邮箱配置时发生错误：' + error.message);
        // 使用模拟数据作为备选方案
        emailConfigs = [];
        updateEmailConfigTable();
    });
}

function refreshAttachmentsList() {
    fetch(API_ENDPOINTS.attachments)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            attachmentsList = data.data || [];
            updateAttachmentsTable();
            updateAttachmentStats();
            addLogEntry('info', `附件列表已刷新，共${attachmentsList.length}个附件`);
        } else {
            addLogEntry('error', '获取附件列表失败：' + (data.error || data.message || '未知错误'));
            console.error('附件API响应:', data);
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '刷新附件列表时发生错误：' + error.message);
        // 使用模拟数据作为备选方案
        attachmentsList = [];
        updateAttachmentsTable();
    });
}

function refreshSummaryData() {
    fetch(API_ENDPOINTS.summary)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateSummaryStats(data.data);
            refreshDataPreview(data.data.records || []);
            addLogEntry('info', '汇总数据已刷新');
        } else {
            addLogEntry('error', '获取汇总数据失败：' + (data.error || data.message || '未知错误'));
            console.error('汇总API响应:', data);
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '刷新汇总数据时发生错误：' + error.message);
        // 使用默认空数据
        updateSummaryStats({});
        refreshDataPreview([]);
    });
}

// ===== 邮箱配置管理功能 =====
function showAddEmailModal() {
    document.getElementById('modalTitle').textContent = '新增邮箱配置';
    document.getElementById('emailConfigForm').reset();
    document.getElementById('configId').value = '';

    // 隐藏帮助信息
    document.getElementById('authCodeHelp').classList.add('d-none');

    // 设置默认值 - 网易企业邮箱配置
    document.getElementById('imapServer').value = 'imap.qiye.163.com';
    document.getElementById('imapPort').value = '993';
    document.getElementById('subjectKeywords').value = '宜欣 生产订单';
    document.getElementById('downloadPath').value = 'downloads/email_attachments';
    document.getElementById('maxAttachmentSize').value = '50';
    document.getElementById('allowedExtensions').value = '.xlsx,.xls,.csv';
    document.getElementById('enableSSL').checked = true;
    document.getElementById('markAsRead').checked = true;

    const modal = new bootstrap.Modal(document.getElementById('emailConfigModal'));
    modal.show();
}

function showAuthCodeHelp() {
    const helpDiv = document.getElementById('authCodeHelp');
    if (helpDiv.classList.contains('d-none')) {
        helpDiv.classList.remove('d-none');
    } else {
        helpDiv.classList.add('d-none');
    }
}

function saveEmailConfig() {
    const formData = new FormData(document.getElementById('emailConfigForm'));
    const configData = Object.fromEntries(formData);
    
    // 验证必填字段
    if (!configData.configName || !configData.emailAddress || !configData.emailPassword || !configData.imapServer || !configData.subjectKeywords || !configData.downloadPath) {
        addLogEntry('error', '请填写所有必填字段');
        return;
    }
    
    // 构建配置对象，字段名与EmailConfig模型保持一致
    const config = {
        name: configData.configName,
        email: configData.emailAddress,          // 修正：使用email（与模型一致）
        password: configData.emailPassword,
        server: configData.imapServer,           // 修正：使用server（与模型一致）
        port: parseInt(configData.imapPort) || 993,  // 修正：使用port（与模型一致）
        senders: configData.senderFilter || null,
        subjects: configData.subjectKeywords,
        check_interval: parseInt(configData.checkInterval) || 60,
        work_start_time: configData.workStartTime || '08:00',
        work_end_time: configData.workEndTime || '18:00',
        download_path: configData.downloadPath,
        use_date_folder: configData.useDateFolder === 'on',
        fetch_days: parseInt(configData.fetchDays) || 10,
        enabled: configData.enableConfig === 'on'
    };
    
    const isEdit = configData.configId && configData.configId !== '';
    const method = isEdit ? 'PUT' : 'POST';
    const url = isEdit ? `${API_ENDPOINTS.emailConfig}/${configData.configId}` : API_ENDPOINTS.emailConfig;
    
    addLogEntry('info', `${isEdit ? '更新' : '添加'}邮箱配置...`);
    console.log('保存配置:', config);
    console.log('API端点:', url);
    console.log('HTTP方法:', method);
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        console.log('API响应:', data);
        if (data.success || data.status === 'success') {
            addLogEntry('success', `邮箱配置${isEdit ? '更新' : '添加'}成功`);
            bootstrap.Modal.getInstance(document.getElementById('emailConfigModal')).hide();
            refreshEmailConfigs();
        } else {
            addLogEntry('error', `邮箱配置${isEdit ? '更新' : '添加'}失败：${data.error || data.message || '未知错误'}`);
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        console.error('保存配置错误:', error);
        addLogEntry('error', `${isEdit ? '更新' : '保存'}邮箱配置时发生错误：` + error.message);
    });
}

function testEmailConfigInModal() {
    const emailAddress = document.getElementById('emailAddress').value;
    const emailPassword = document.getElementById('emailPassword').value;
    const imapServer = document.getElementById('imapServer').value;
    const imapPort = document.getElementById('imapPort').value;
    
    if (!emailAddress || !emailPassword || !imapServer) {
        addLogEntry('error', '请先填写邮箱基本信息');
        return;
    }
    
    // 修正字段映射，与EmailConfig模型字段名匹配
    const testConfig = {
        email: emailAddress,              // 使用email（与模型一致）
        password: emailPassword,
        server: imapServer,               // 使用server（与模型一致）
        port: parseInt(imapPort) || 993,  // 使用port（与模型一致）
        enable_ssl: document.getElementById('enableSSL')?.checked || true
    };
    
    addLogEntry('info', '正在测试邮箱连接...');
    console.log('测试配置:', testConfig);
    console.log('API端点:', `${API_ENDPOINTS.emailConfig}/test`);
    
    // 禁用测试按钮，防止重复点击
    const testBtn = document.querySelector('button[onclick="testEmailConfigInModal()"]');
    if (testBtn) {
        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>测试中...';
    }
    
    fetch(`${API_ENDPOINTS.emailConfig}/test`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(testConfig)
    })
    .then(response => response.json())
    .then(data => {
        console.log('测试连接响应:', data);
        if (data.status === 'success') {
            addLogEntry('success', '邮箱连接测试成功！可以正常收取邮件');
        } else {
            const errorMsg = data.message || '未知错误';
            if (errorMsg.includes('ERR.LOGIN.REQCODE')) {
                addLogEntry('error', '邮箱连接测试失败：请使用授权码而非登录密码。请到网易企业邮箱设置中生成客户端授权码');
            } else {
                addLogEntry('error', '邮箱连接测试失败：' + errorMsg);
            }
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        console.error('测试连接错误:', error);
        addLogEntry('error', '邮箱连接测试时发生错误：' + error.message);
    })
    .finally(() => {
        // 恢复测试按钮
        if (testBtn) {
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-plug me-1"></i>测试连接';
        }
    });
}

function testEmailConnection() {
    const selectedConfigId = document.getElementById('testEmailSelect').value;
    if (!selectedConfigId) {
        addLogEntry('warning', '请选择要测试的邮箱配置');
        return;
    }
    
    addLogEntry('info', '正在测试邮箱连接...');
    document.getElementById('testConnectionBtn').disabled = true;
    
    fetch(`${API_ENDPOINTS.emailConfig}/${selectedConfigId}/test`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            addLogEntry('success', '邮箱连接测试成功');
            document.getElementById('testResult').innerHTML = '<small class="text-success">✓ 连接正常</small>';
        } else {
            addLogEntry('error', '邮箱连接测试失败：' + (data.message || '未知错误'));
            document.getElementById('testResult').innerHTML = '<small class="text-danger">✗ 连接失败</small>';
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '邮箱连接测试时发生错误：' + error.message);
        document.getElementById('testResult').innerHTML = '<small class="text-danger">✗ 连接异常</small>';
    })
    .finally(() => {
        document.getElementById('testConnectionBtn').disabled = false;
    });
}

function previewAttachments() {
    const selectedConfigId = document.getElementById('previewEmailSelect').value;
    if (!selectedConfigId) {
        addLogEntry('warning', '请选择要预览的邮箱配置');
        return;
    }
    
    addLogEntry('info', '正在预览已下载的Excel附件...');
    document.getElementById('previewBtn').disabled = true;
    
    fetch(`${API_ENDPOINTS.emailConfig}/${selectedConfigId}/preview`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            days: null  // 使用邮箱配置中设置的抓取天数
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const attachments = data.data.attachments || [];
            const count = attachments.length;
            const configName = data.data.config_name || '未知配置';
            const downloadPath = data.data.download_path || '';
            const days = data.data.days_searched || 0;
            
            let message = `${configName}: 最近${days}天已下载${count}个Excel附件`;
            
            // 统计文件状态
            const existingFiles = attachments.filter(att => att.file_exists).length;
            const missingFiles = count - existingFiles;
            const processedFiles = attachments.filter(att => att.processed).length;
            
            if (count > 0) {
                message += `\n- 文件完整: ${existingFiles}个`;
                if (missingFiles > 0) {
                    message += `\n- 文件丢失: ${missingFiles}个`;
                }
                message += `\n- 已处理: ${processedFiles}个`;
                message += `\n- 存储路径: ${downloadPath}`;
                
                // 显示详细的附件列表
                showAttachmentPreviewModal(attachments, configName);
            }
            
            addLogEntry('success', message);
            document.getElementById('previewResult').innerHTML = `
                <div class="small text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    找到 ${count} 个已下载的Excel附件
                    ${missingFiles > 0 ? `<span class="text-warning ms-2">(${missingFiles}个文件丢失)</span>` : ''}
                </div>
            `;
        } else {
            addLogEntry('error', '预览附件失败：' + (data.error || '未知错误'));
            document.getElementById('previewResult').innerHTML = '<small class="text-danger"><i class="fas fa-times-circle me-1"></i>预览失败</small>';
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '预览附件时发生错误：' + error.message);
        document.getElementById('previewResult').innerHTML = '<small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>预览异常</small>';
    })
    .finally(() => {
        document.getElementById('previewBtn').disabled = false;
    });
}

// 显示附件预览模态框
function showAttachmentPreviewModal(attachments, configName) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="attachmentPreviewModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-file-excel me-2"></i>已下载的Excel附件预览 - ${configName}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive" style="max-height: 500px;">
                            <table class="table table-hover table-sm">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th>文件名</th>
                                        <th>发件人</th>
                                        <th>接收时间</th>
                                        <th>文件大小</th>
                                        <th>状态</th>
                                        <th>处理状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${attachments.map(att => `
                                        <tr class="${att.file_exists ? '' : 'table-warning'}">
                                            <td>
                                                <i class="fas fa-file-excel text-success me-1"></i>
                                                ${att.filename}
                                            </td>
                                            <td class="small text-muted">${att.sender}</td>
                                            <td class="small">${att.receive_date}</td>
                                            <td class="small">${formatFileSize(att.file_size)}</td>
                                            <td>
                                                <span class="badge ${att.file_exists ? 'bg-success' : 'bg-warning'}">
                                                    ${att.status}
                                                </span>
                                            </td>
                                            <td>
                                                ${att.processed ? `
                                                    <span class="badge ${
                                                        att.process_result === 'success' ? 'bg-success' : 
                                                        att.process_result === 'error' ? 'bg-danger' : 'bg-secondary'
                                                    }">
                                                        ${att.process_result === 'success' ? '已处理' : 
                                                          att.process_result === 'error' ? '处理失败' : '已处理'}
                                                    </span>
                                                ` : '<span class="badge bg-light text-dark">未处理</span>'}
                                            </td>
                                            <td>
                                                ${att.file_exists ? `
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="processAttachment(${att.id})" title="处理此附件">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                ` : `
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" disabled title="文件不存在">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                    </button>
                                                `}
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="batchProcessAttachments()">
                            <i class="fas fa-play me-1"></i>批量处理
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除现有模态框并添加新的
    const existingModal = document.getElementById('attachmentPreviewModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('attachmentPreviewModal'));
    modal.show();
}

// ===== 表格更新功能 =====
function updateEmailConfigTable() {
    const tbody = document.getElementById('emailConfigTableBody');
    const countElement = document.getElementById('emailConfigCount');
    
    if (countElement) countElement.textContent = emailConfigs.length;
    
    if (!tbody) return;
    
    if (emailConfigs.length === 0) {
        tbody.innerHTML = `
            <tr id="noEmailConfigRow">
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                    暂无邮箱配置，请点击"新增邮箱"开始配置
                </td>
            </tr>
        `;
        return;
    }
    
    const rows = emailConfigs.map(config => `
        <tr>
            <td>${config.email || '未知邮箱'}</td>
            <td>${config.server || 'unknown'}:${config.port || 993}</td>
            <td>
                ${config.subjects ? config.subjects.split(',').map(keyword => 
                    `<span class="badge bg-secondary me-1">${keyword.trim()}</span>`
                ).join('') : ''}
            </td>
            <td>
                <span class="badge ${config.enabled ? 'bg-success' : 'bg-secondary'}">
                    ${config.enabled ? '启用' : '禁用'}
                </span>
            </td>
            <td class="small">${config.updated_at ? formatDateTime(config.updated_at) : '从未更新'}</td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editEmailConfig(${config.id})">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteEmailConfig(${config.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = rows;
}

function updateEmailConfigSelects() {
    const testSelect = document.getElementById('testEmailSelect');
    const previewSelect = document.getElementById('previewEmailSelect');
    
    const options = emailConfigs.map(config => 
        `<option value="${config.id}">${config.email}</option>`
    ).join('');
    
    if (testSelect) {
        testSelect.innerHTML = '<option value="">选择要测试的邮箱配置</option>' + options;
    }
    if (previewSelect) {
        previewSelect.innerHTML = '<option value="">选择要预览的邮箱配置</option>' + options;
    }
}

function updateAttachmentsList(attachments = []) {
    attachmentsList = attachments;
    updateAttachmentsTable();
}

function updateAttachmentsTable() {
    const tbody = document.getElementById('attachmentTableBody');
    const countElement = document.getElementById('attachmentListCount');
    
    if (countElement) countElement.textContent = attachmentsList.length;
    
    if (!tbody) return;
    
    if (attachmentsList.length === 0) {
        tbody.innerHTML = `
            <tr id="noAttachmentsRow">
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                    暂无附件，请点击"扫描"按钮查找Excel文件
                </td>
            </tr>
        `;
        return;
    }
    
    const rows = attachmentsList.map(attachment => `
        <tr data-filepath="${attachment.filepath}" data-filename="${attachment.filename}">
            <td>
                <input type="checkbox" class="attachment-checkbox" value="${attachment.id}" onchange="updateSelectedAttachmentsCount()">
            </td>
            <td>
                <i class="fas fa-file-excel text-success me-1"></i>
                ${attachment.filename || '未知文件'}
            </td>
            <td>${formatFileSize(attachment.size || 0)}</td>
            <td>${formatDateTime(attachment.received_time)}</td>
            <td><span class="badge bg-info">${attachment.folder || '-'}</span></td>
            <td>
                <span class="badge ${getStatusBadgeClass(attachment.status)}">
                    ${getStatusText(attachment.status)}
                </span>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="processSingleAttachment('${attachment.filepath}', '${attachment.filename}')" title="处理此附件">
                    <i class="fas fa-play"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = rows;
    
    // 更新选中按钮状态
    updateSelectedAttachmentsCount();
}

function updateAttachmentStats() {
    const total = attachmentsList.length;
    // 修复：使用与API一致的processed字段来判断是否已处理
    const processed = attachmentsList.filter(a => a.processed === true || a.status === 'completed').length;
    const pending = total - processed;
    // 错误状态：process_result为error的
    const errors = attachmentsList.filter(a => a.process_result === 'error').length;
    
    const elements = {
        'totalAttachmentsCount': total,
        'processedCount': processed,
        'pendingCount': pending,
        'errorCount': errors
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = value;
    });
    
    console.log('📊 附件状态统计:', elements);
}

function updateSummaryStats(stats = {}) {
    const elements = {
        'totalRecordsCount': stats.total_records || 0,
        'validRecordsCount': stats.valid_records || 0,
        'duplicateRecordsCount': stats.duplicate_records || 0,
        'uniqueProductsCount': stats.unique_products || 0,
        'engineeringOrdersCount': stats.engineering_orders || 0,
        'productionOrdersCount': stats.production_orders || 0
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = value;
    });
    
    // 更新产品筛选器
    const productSelect = document.getElementById('productFilter');
    if (productSelect && stats.product_list && stats.product_list.length > 0) {
        const options = stats.product_list.map(product => 
            `<option value="${product}">${product}</option>`
        ).join('');
        productSelect.innerHTML = options;
    }
}

// ===== 任务控制功能 =====
function startProcessing(mode = 'auto') {
    console.log('🔵 点击自动按钮，模式:', mode);
    
    if (isProcessing) {
        addLogEntry('warning', '已有任务在执行中');
        return;
    }
    
    // 验证邮箱配置
    if (emailConfigs.length === 0) {
        addLogEntry('error', '没有可用的邮箱配置，请先配置邮箱');
        switchStep(1);
        return;
    }
    
    const activeConfigs = emailConfigs.filter(config => config.enabled === true);
    if (activeConfigs.length === 0) {
        addLogEntry('error', '没有活跃的邮箱配置，请先启用邮箱配置');
        switchStep(1);
        return;
    }
    
    // 获取解析器设置
    const parseMode = document.querySelector('input[name="parseMode"]:checked')?.value || 'auto';
    const useEnhancedParser = document.getElementById('useEnhancedParser')?.checked || true;
    
    // 准备处理数据
    const processingData = {
        mode: mode,
        email_configs: activeConfigs.map(config => config.id),
        parse_settings: {
            mode: parseMode,
            use_enhanced_parser: useEnhancedParser,  // 确保传递解析器选择
            skip_duplicates: document.getElementById('skipDuplicates')?.checked || true,
            backup_original: document.getElementById('backupOriginal')?.checked || true
        }
    };
    
    // 记录解析器选择
    addLogEntry('info', `使用${useEnhancedParser ? '增强' : '标准'}解析器，模式: ${parseMode}`);
    
    // 更新UI状态
    isProcessing = true;
    updateProcessingButtons(true);
    updateTaskStatus('processing');
    addLogEntry('info', `开始${mode === 'auto' ? '自动' : '分步'}处理任务...`);
    
    // 发送请求
    fetch(`${API_ENDPOINTS.processing}/start`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(processingData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('success', `任务启动成功: ${data.task_id}`);
            currentTaskId = data.task_id;
        } else {
            addLogEntry('error', `任务启动失败: ${data.error}`);
            isProcessing = false;
            updateProcessingButtons(false);
            updateTaskStatus('error');
        }
    })
    .catch(error => {
        console.error('任务启动请求失败:', error);
        addLogEntry('error', `任务启动请求失败: ${error.message}`);
        isProcessing = false;
        updateProcessingButtons(false);
        updateTaskStatus('error');
    });
}

function pauseProcessing() {
    if (!currentTaskId || !isProcessing) {
        addLogEntry('warning', '没有正在执行的任务');
        return;
    }
    
    fetch(`${API_ENDPOINTS.processing}/${currentTaskId}/pause`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('info', '任务已暂停');
            updateTaskStatus('paused');
        } else {
            addLogEntry('error', '暂停任务失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '暂停任务时发生错误：' + error.message);
    });
}

function stopProcessing() {
    if (!currentTaskId) {
        addLogEntry('warning', '没有正在执行的任务');
        return;
    }
    
    fetch(`${API_ENDPOINTS.processing}/${currentTaskId}/stop`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('info', '任务已停止');
            resetProcessingState();
        } else {
            addLogEntry('error', '停止任务失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '停止任务时发生错误：' + error.message);
    });
}

function resetProcessingState() {
    isProcessing = false;
    currentTaskId = null;
    updateProcessingButtons(false);
    updateTaskStatus('idle');
    document.getElementById('currentTaskId').textContent = '-';
    updateTaskProgress({ overall: 0, step: 0, message: '待命中' });
}

function updateProcessingButtons(processing) {
    const buttons = {
        'startBtn': !processing,
        'stepBtn': !processing,
        'pauseBtn': processing,
        'stopBtn': processing
    };
    
    Object.entries(buttons).forEach(([id, enabled]) => {
        const button = document.getElementById(id);
        if (button) button.disabled = !enabled;
    });
}

// ===== 步骤控制功能 =====
function switchStep(stepNumber) {
    console.log('🔄 切换步骤开始:', stepNumber, '当前步骤:', currentStep);
    
    if (stepNumber === currentStep) {
        console.log('⚠️ 步骤相同，跳过切换');
        return;
    }
    
    console.log('📝 开始更新步骤指示器...');
    // 更新步骤指示器
    document.querySelectorAll('.step-item').forEach((item, index) => {
        const stepNum = index + 1;
        console.log(`  - 处理步骤 ${stepNum}:`, item);
        
        // 移除所有状态类
        item.classList.remove('step-active', 'step-completed');
        
        // 添加相应状态类
        if (stepNum === stepNumber) {
            item.classList.add('step-active');
            console.log(`  ✅ 步骤 ${stepNum} 设为激活状态`);
        } else if (stepNum < stepNumber) {
            item.classList.add('step-completed');
            console.log(`  ✅ 步骤 ${stepNum} 设为完成状态`);
        }
    });
    
    console.log('📱 开始显示对应面板...');
    // 显示对应面板
    document.querySelectorAll('.step-panel').forEach((panel, index) => {
        const panelNumber = index + 1;
        const shouldBeActive = panelNumber === stepNumber;
        
        console.log(`  - 面板 ${panelNumber}:`, panel.id, '应该显示:', shouldBeActive);
        
        if (shouldBeActive) {
            panel.classList.add('active');
            console.log(`  ✅ 面板 ${panelNumber} 已激活`);
        } else {
            panel.classList.remove('active');
            console.log(`  ❌ 面板 ${panelNumber} 已隐藏`);
        }
    });
    
    currentStep = stepNumber;
    console.log('✅ 步骤切换完成，新步骤:', currentStep);
    addLogEntry('info', `切换到步骤${stepNumber}`);
    
    // 如果切换到步骤3，初始化数据
    if (stepNumber === 3) {
        console.log('🎯 准备初始化步骤3...');
        setTimeout(() => {
            console.log('🚀 开始初始化步骤3');
            try {
                initializeStep3();
                console.log('✅ 步骤3初始化完成');
            } catch (error) {
                console.error('❌ 步骤3初始化出错:', error);
                addLogEntry('error', '步骤3初始化失败: ' + error.message);
            }
        }, 100);
    }
}

// ===== 辅助函数 =====
function updateConnectionStatus(status) {
    const statusElement = document.getElementById('taskStatus');
    if (!statusElement) return;
    
    const statusMap = {
        'idle': { class: 'status-idle', text: '待命中', icon: 'fas fa-circle' },
        'connecting': { class: 'status-warning', text: '连接中', icon: 'fas fa-circle pulse' },
        'connected': { class: 'status-success', text: '已连接', icon: 'fas fa-circle' },
        'processing': { class: 'status-processing', text: '处理中', icon: 'fas fa-spinner fa-spin' },
        'paused': { class: 'status-warning', text: '已暂停', icon: 'fas fa-pause' },
        'error': { class: 'status-error', text: '错误', icon: 'fas fa-exclamation-triangle' }
    };
    
    const statusInfo = statusMap[status] || statusMap['idle'];
    statusElement.className = `status-indicator ${statusInfo.class}`;
    statusElement.innerHTML = `<i class="${statusInfo.icon} me-1"></i>${statusInfo.text}`;
}

function updateTaskStatus(status) {
    updateConnectionStatus(status);
    processingStatus = status;
}

function updateTaskProgress(progressData) {
    console.log('🔄 更新任务进度:', progressData);
    
    try {
        // 兼容不同的数据结构
        const overall = progressData.progress || progressData.overall || 0;
        const step = progressData.step_progress || progressData.step || 0;
        const message = progressData.message || '处理中...';
        const status = progressData.status || 'running';
        
        console.log(`📊 进度数据: overall=${overall}%, step=${step}%, message="${message}", status="${status}"`);
        
        // 更新总体进度
        const overallProgressElement = document.getElementById('overallProgress');
        const overallProgressBar = document.getElementById('overallProgressBar');
        if (overallProgressElement) {
            overallProgressElement.textContent = overall + '%';
            console.log('✅ 已更新总体进度文本:', overall + '%');
        }
        if (overallProgressBar) {
            overallProgressBar.style.width = overall + '%';
            overallProgressBar.setAttribute('aria-valuenow', overall);
            console.log('✅ 已更新总体进度条:', overall + '%');
        }
        
        // 更新步骤进度
        const stepProgressElement = document.getElementById('stepProgress');
        const stepProgressBar = document.getElementById('stepProgressBar');
        if (stepProgressElement) {
            stepProgressElement.textContent = step + '%';
            console.log('✅ 已更新步骤进度文本:', step + '%');
        }
        if (stepProgressBar) {
            stepProgressBar.style.width = step + '%';
            stepProgressBar.setAttribute('aria-valuenow', step);
            console.log('✅ 已更新步骤进度条:', step + '%');
        }
        
        // 更新进度消息
        const progressMessageElement = document.getElementById('progressMessage');
        if (progressMessageElement) {
            progressMessageElement.textContent = message;
            console.log('✅ 已更新进度消息:', message);
        }
        
        // 更新任务状态
        const taskStatusElement = document.getElementById('taskStatus');
        if (taskStatusElement) {
            // 更新状态指示器
            taskStatusElement.className = `status-indicator status-${status}`;
            taskStatusElement.textContent = getStatusDisplayText(status);
            console.log('✅ 已更新任务状态:', status);
        }
        
        // 同时更新日志，显示实时进度
        if (message && message !== '处理中...' && message !== '准备就绪，等待开始...') {
            addLogEntry('info', `进度 ${overall}%: ${message}`);
        }
        
    } catch (error) {
        console.error('❌ 更新任务进度时出错:', error);
        addLogEntry('error', '更新进度显示时出错: ' + error.message);
    }
}

// 获取状态显示文本
function getStatusDisplayText(status) {
    const statusMap = {
        'idle': '空闲',
        'running': '运行中', 
        'processing': '处理中',
        'completed': '已完成',
        'error': '错误',
        'paused': '已暂停'
    };
    return statusMap[status] || status;
}

function handleTaskComplete(data) {
    addLogEntry('success', '任务执行完成');
    resetProcessingState();
    
    // 刷新所有数据
    setTimeout(() => {
        refreshEmailConfigs();
        refreshAttachmentsList();
        refreshSummaryData();
    }, 1000);
}

function handleTaskError(data) {
    addLogEntry('error', '任务执行出错：' + (data.error || '未知错误'));
    resetProcessingState();
}

// 日志自动滚动状态
let logAutoScroll = true;

function addLogEntry(type, message) {
    const timestamp = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    // 输出到控制台
    console.log(`[${timestamp}] [${type.toUpperCase()}] ${message}`);

    // 在页面上显示日志
    const logContent = document.getElementById('logContent');
    if (logContent) {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;

        // 根据日志类型设置样式
        let textClass = 'text-muted';
        let icon = 'fas fa-info-circle';

        switch (type) {
            case 'success':
                textClass = 'text-success';
                icon = 'fas fa-check-circle';
                break;
            case 'error':
                textClass = 'text-danger';
                icon = 'fas fa-exclamation-circle';
                break;
            case 'warning':
                textClass = 'text-warning';
                icon = 'fas fa-exclamation-triangle';
                break;
            case 'info':
                textClass = 'text-info';
                icon = 'fas fa-info-circle';
                break;
        }

        logEntry.innerHTML = `
            <span class="log-timestamp text-muted">[${timestamp}]</span>
            <i class="${icon} me-1 ${textClass}"></i>
            <span class="log-message ${textClass}">${message}</span>
        `;

        logContent.appendChild(logEntry);

        // 限制日志条数，避免内存占用过多
        const maxLogEntries = 500;
        const logEntries = logContent.querySelectorAll('.log-entry');
        if (logEntries.length > maxLogEntries) {
            // 删除最旧的日志条目
            for (let i = 0; i < logEntries.length - maxLogEntries; i++) {
                logEntries[i].remove();
            }
        }

        // 自动滚动到底部
        if (logAutoScroll) {
            logContent.scrollTop = logContent.scrollHeight;
        }
    }

    // 对于重要消息，可以使用浏览器原生通知
    if (type === 'error') {
        console.error(`错误: ${message}`);
    } else if (type === 'success') {
        console.info(`成功: ${message}`);
    }
}

// 弹框提示功能 - 已禁用，使用实时日志替代
function showToast(type, message, duration = 5000) {
    // 弹窗通知已完全禁用，所有通知信息通过实时日志显示
    // 页面已有完整的实时日志功能，无需额外弹窗干扰用户
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // 可选：如果需要在日志中记录这类消息，可以调用 addLogEntry
    // addLogEntry(type, message);
}

// 获取toast图标
function getToastIcon(type) {
    const iconMap = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return iconMap[type] || 'fa-info-circle';
}

// 显示授权码帮助信息
function showAuthCodeHelp() {
    const helpDiv = document.getElementById('authCodeHelp');
    if (helpDiv) {
        helpDiv.classList.toggle('d-none');
    }
}

// 清空日志
function clearLogs() {
    const logContent = document.getElementById('logContent');
    if (logContent) {
        logContent.innerHTML = `
            <div class="log-entry text-muted">
                <span class="log-timestamp">[系统]</span>
                <span class="log-message">日志已清空</span>
            </div>
        `;
    }
    console.clear();
}

// 切换自动滚动
function toggleLogAutoScroll() {
    logAutoScroll = !logAutoScroll;
    const btn = document.getElementById('autoScrollBtn');
    if (btn) {
        if (logAutoScroll) {
            btn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>自动滚动';
            btn.classList.remove('btn-outline-primary');
            btn.classList.add('btn-primary');

            // 立即滚动到底部
            const logContent = document.getElementById('logContent');
            if (logContent) {
                logContent.scrollTop = logContent.scrollHeight;
            }
        } else {
            btn.innerHTML = '<i class="fas fa-pause me-1"></i>已暂停';
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-primary');
        }
    }
}

function filterLogs(filterType) {
    // 日志筛选功能已移除
    console.log('日志筛选功能已移除，请使用浏览器开发者工具查看日志');
}

function downloadLogs() {
    // 日志下载功能已移除
    console.log('日志下载功能已移除，请使用浏览器开发者工具导出日志');
}

// 格式化函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDateTime(dateTime) {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleString('zh-CN');
}

function getStatusBadgeClass(status) {
    const statusMap = {
        'pending': 'bg-warning',
        'processing': 'bg-info',
        'completed': 'bg-success',
        'error': 'bg-danger'
    };
    return statusMap[status] || 'bg-secondary';
}

function getStatusText(status) {
    const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'error': '处理失败'
    };
    return statusMap[status] || '未知';
}

function handleDateRangeChange() {
    const dateRange = document.getElementById('dateRangeFilter')?.value;
    const customRange = document.getElementById('customDateRange');
    
    if (!customRange) return;
    
    if (dateRange === 'custom') {
        customRange.style.display = 'block';
    } else {
        customRange.style.display = 'none';
        
        // 自动设置日期范围
        const today = new Date();
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        
        if (startDate && endDate) {
            switch (dateRange) {
                case 'today':
                    const todayStr = today.toISOString().split('T')[0];
                    startDate.value = todayStr;
                    endDate.value = todayStr;
                    break;
                case 'week':
                    const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
                    startDate.value = weekStart.toISOString().split('T')[0];
                    endDate.value = new Date().toISOString().split('T')[0];
                    break;
                case 'month':
                    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                    startDate.value = monthStart.toISOString().split('T')[0];
                    endDate.value = new Date().toISOString().split('T')[0];
                    break;
            }
        }
    }
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    if (websocketConnection) {
        websocketConnection.close();
    }
});

// ===== 真实的附件处理功能 =====

function scanAttachments() {
    const scanBtn = document.getElementById('scanAttachmentsBtn');
    const originalText = scanBtn.innerHTML;
    
    scanBtn.disabled = true;
    scanBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>扫描中...';
    
    addLogEntry('info', '🔍 正在扫描邮箱中的Excel附件（预览模式）...');
    
    // 获取扫描参数
    const scanDays = parseInt(document.getElementById('emailScanDays')?.value) || 7;
    const emailConfigId = document.getElementById('emailConfigSelect')?.value || null;
    
    fetch(`${API_ENDPOINTS.attachments}/scan`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            email_config_id: emailConfigId,
            days: scanDays
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            attachmentsList = data.data || [];
            updateAttachmentsList(attachmentsList);
            updateAttachmentStats();
            
            // 检查是否为预览模式
            if (data.preview_mode) {
                addLogEntry('info', `📧 邮箱扫描完成：在 ${data.email_config?.email || '邮箱'} 中发现 ${attachmentsList.length} 个Excel附件`);
                addLogEntry('warning', '⚠️ 这些是邮箱中的附件预览，需要点击"自动处理"按钮才能下载');
                
                // 在附件表格上方显示提示
                const attachmentContainer = document.querySelector('#step2Panel .card-body');
                if (attachmentContainer) {
                    let alertElement = attachmentContainer.querySelector('.preview-alert');
                    if (!alertElement) {
                        alertElement = document.createElement('div');
                        alertElement.className = 'alert alert-info preview-alert mb-3';
                        attachmentContainer.insertBefore(alertElement, attachmentContainer.firstChild);
                    }
                    alertElement.innerHTML = `
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>预览模式：</strong>显示的是邮箱中的附件列表，需要先点击"自动处理"按钮下载这些附件。
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="startAutoProcessing()">
                            立即下载
                        </button>
                    `;
                }
                
                // 自动切换到第二步
                if (attachmentsList.length > 0) {
                    switchStep(2);
                }
            } else {
                // 本地文件模式
            const message = data.message || `扫描完成：发现 ${attachmentsList.length} 个Excel文件`;
            addLogEntry('success', message);
            
            // 自动切换到第二步
            if (attachmentsList.length > 0) {
                switchStep(2);
            }
            }
            
            console.log('📊 扫描统计信息:', data.statistics || {});
        } else {
            addLogEntry('error', '📧 邮箱扫描失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('扫描请求失败:', error);
        addLogEntry('error', '扫描时发生错误：' + error.message);
    })
    .finally(() => {
        scanBtn.disabled = false;
        scanBtn.innerHTML = originalText;
    });
}



function processSelectedAttachments() {
    const selectedCheckboxes = document.querySelectorAll('.attachment-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        addLogEntry('warning', '请先选择要处理的附件');
        return;
    }
    
    const selectedFiles = Array.from(selectedCheckboxes).map(cb => {
        const row = cb.closest('tr');
        return {
            id: cb.value,
            filepath: row.dataset.filepath,
            filename: row.dataset.filename
        };
    });
    
    addLogEntry('info', `开始处理 ${selectedFiles.length} 个选中的附件...`);
    
    // 逐个处理选中的文件
    let processed = 0;
    let successful = 0;
    
    selectedFiles.forEach((file, index) => {
        setTimeout(() => {
            processSingleFile(file).then(result => {
                processed++;
                if (result.success) successful++;
                
                addLogEntry(result.success ? 'success' : 'error', 
                    `${file.filename}: ${result.message}`);
                
                if (processed === selectedFiles.length) {
                    addLogEntry('info', `批量处理完成：${successful}/${processed} 成功`);
                    refreshDataPreview();
                    refreshSummaryData();
                }
            });
        }, index * 200); // 错开处理时间
    });
}

function processBatchAttachments() {
    if (attachmentsList.length === 0) {
        addLogEntry('warning', '没有发现可处理的附件，请先扫描');
        return;
    }
    
    const batchBtn = document.getElementById('processBatchBtn');
    const originalText = batchBtn.innerHTML;
    
    batchBtn.disabled = true;
    batchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>批量处理中...';
    
    addLogEntry('info', `开始批量处理 ${attachmentsList.length} 个Excel文件...`);
    
    const filePaths = attachmentsList.map(file => file.filepath);
    
    fetch(`${API_ENDPOINTS.attachments}/process-batch`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            file_paths: filePaths,
            max_files: 50
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const results = data.data;
            
            addLogEntry('success', 
                `批量处理完成：${results.successful_parses}/${results.total_files} 成功解析`);
            addLogEntry('info', 
                `共提取 ${results.total_records} 条记录（标准：${results.standard_orders}，CP：${results.cp_orders}）`);
            
            // 更新统计数据
            updateBatchProcessingResults(results);
            
            // 自动切换到第三步
            switchStep(3);
            refreshDataPreview();
            refreshSummaryData();
        } else {
            addLogEntry('error', '批量处理失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('批量处理请求失败:', error);
        addLogEntry('error', '批量处理时发生错误：' + error.message);
    })
    .finally(() => {
        batchBtn.disabled = false;
        batchBtn.innerHTML = originalText;
    });
}

function processSingleFile(file) {
    return fetch(`${API_ENDPOINTS.attachments}/1/process`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            filepath: file.filepath
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return {
                success: true,
                message: `解析成功：${data.data.records_count} 条记录`
            };
        } else {
            return {
                success: false,
                message: data.error || '解析失败'
            };
        }
    })
    .catch(error => {
        return {
            success: false,
            message: '处理时发生错误：' + error.message
        };
    });
}

function refreshDataPreview() {
    addLogEntry('info', '刷新数据预览...');
    
    fetch(`${API_ENDPOINTS.data}/preview?limit=100`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const records = data.data || [];
            updateDataPreviewTable(records);
            updateDataStats(data.stats);
            
            addLogEntry('success', `数据预览已更新：${records.length} 条记录`);
        } else {
            addLogEntry('error', '刷新数据预览失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('刷新数据预览失败:', error);
        addLogEntry('error', '刷新数据预览时发生错误：' + error.message);
    });
}

function exportSelectedData() {
    const selectedCheckboxes = document.querySelectorAll('.record-checkbox:checked');
    const dateRange = document.getElementById('dateRangeFilter')?.value || 'all';
    const orderType = document.getElementById('orderTypeFilter')?.value || 'all';
    
    addLogEntry('info', '准备导出数据...');
    
    const exportData = {
        format: 'json',
        filters: {
            date_range: dateRange,
            order_type: orderType,
            selected_only: selectedCheckboxes.length > 0
        }
    };
    
    if (selectedCheckboxes.length > 0) {
        exportData.selected_ids = Array.from(selectedCheckboxes).map(cb => cb.value);
    }
    
    fetch(`${API_ENDPOINTS.data}/export`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(exportData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const downloadUrl = data.data.download_url;
            const filename = data.data.filename;
            
            // 创建下载链接
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            addLogEntry('success', `数据导出成功：${filename} (${data.data.total_records} 条记录)`);
        } else {
            addLogEntry('error', '数据导出失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('导出请求失败:', error);
        addLogEntry('error', '导出数据时发生错误：' + error.message);
    });
}

function applyFilters() {
    const filters = {
        date_range: document.getElementById('dateRangeFilter')?.value || 'all',
        order_type: document.getElementById('orderTypeFilter')?.value || 'all',
        status: document.getElementById('statusFilter')?.value || 'all'
    };
    
    if (filters.date_range === 'custom') {
        filters.date_start = document.getElementById('startDate')?.value;
        filters.date_end = document.getElementById('endDate')?.value;
    }
    
    addLogEntry('info', '应用数据筛选条件...');
    
    // 构建查询参数
    const params = new URLSearchParams();
    if (filters.order_type !== 'all') params.append('type', filters.order_type);
    if (filters.date_start) params.append('date_start', filters.date_start);
    if (filters.date_end) params.append('date_end', filters.date_end);
    
    fetch(`${API_ENDPOINTS.data}/preview?${params.toString()}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const records = data.data || [];
            updateDataPreviewTable(records);
            addLogEntry('success', `筛选完成：显示 ${records.length} 条记录`);
        } else {
            addLogEntry('error', '筛选失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('筛选请求失败:', error);
        addLogEntry('error', '筛选时发生错误：' + error.message);
    });
}

function generateEngineeringReport() {
    addLogEntry('info', '生成FT工程订单报表...');
    applyFilters(); // 使用筛选结果生成报表
}

function generateProductionReport() {
    addLogEntry('info', '生成FT量产订单报表...');
    applyFilters(); // 使用筛选结果生成报表
}

// ===== 辅助函数 =====

function updateSelectedAttachmentsCount() {
    const selectedCheckboxes = document.querySelectorAll('.attachment-checkbox:checked');
    const processSelectedBtn = document.getElementById('processSelectedBtn');
    
    if (processSelectedBtn) {
        processSelectedBtn.disabled = selectedCheckboxes.length === 0;
        if (selectedCheckboxes.length > 0) {
            processSelectedBtn.innerHTML = `<i class="fas fa-play me-1"></i>处理选中 (${selectedCheckboxes.length})`;
        } else {
            processSelectedBtn.innerHTML = '<i class="fas fa-play me-1"></i>处理选中';
        }
    }
}

function toggleAllAttachments() {
    const selectAllCheckbox = document.getElementById('selectAllAttachments');
    const checkboxes = document.querySelectorAll('.attachment-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateSelectedAttachmentsCount();
}

function processSingleAttachment(filepath, filename) {
    addLogEntry('info', `处理文件: ${filename}`);
    
    processSingleFile({ filepath, filename }).then(result => {
        addLogEntry(result.success ? 'success' : 'error', 
            `${filename}: ${result.message}`);
        
        if (result.success) {
            refreshDataPreview();
            refreshSummaryData();
        }
    });
}

function updateDataPreviewTable(records = []) {
    const tbody = document.getElementById('dataPreviewTableBody');
    const countElement = document.getElementById('previewRecordCount');
    
    if (countElement) countElement.textContent = records.length;
    
    if (!tbody) return;
    
    if (records.length === 0) {
        tbody.innerHTML = `
            <tr id="noDataRow">
                <td colspan="9" class="text-center text-muted py-4">
                    <i class="fas fa-database fa-2x mb-2 d-block"></i>
                    暂无数据，请先处理Excel附件
                </td>
            </tr>
        `;
        return;
    }
    
    const rows = records.slice(0, 100).map(record => {
        // 判断记录类型
        const isCP = record.processing_type || record.contractor_name;
        const recordType = isCP ? 'CP订单' : 'FT订单';
        const badgeClass = isCP ? 'bg-primary' : 'bg-success';
        
        // 获取产品名称
        const productName = record.label_name || record.product_name || record.chip_name || '-';
        
        // 获取数量信息
        const quantity = record.processing_pieces || record.package_qty1 || record.package_qty2 || '-';
        
        return `
            <tr>
                <td><input type="checkbox" class="record-checkbox" value="${record.id}" onchange="updateSelectedRecordsCount()"></td>
                <td title="${productName}">${productName.length > 30 ? productName.substring(0, 30) + '...' : productName}</td>
                <td>${quantity}</td>
                <td>${record.delivery_date ? formatDateTime(record.delivery_date) : '-'}</td>
                <td><span class="badge ${badgeClass}">${recordType}</span></td>
                <td title="${record.source_file || '-'}">${record.source_file ? record.source_file.substring(0, 20) + '...' : '-'}</td>
                <td>${record.created_at ? formatDateTime(record.created_at) : '-'}</td>
                <td><span class="badge bg-info">已保存</span></td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="viewRecordDetail('${record.id}', '${recordType}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
    
    tbody.innerHTML = rows;
    updateSelectedRecordsCount();
}

function updateSelectedRecordsCount() {
    const selectedCheckboxes = document.querySelectorAll('.record-checkbox:checked');
    const countElement = document.getElementById('selectedRecordsCount');
    
    if (countElement) {
        countElement.textContent = selectedCheckboxes.length;
    }
}

function toggleAllRecords() {
    const selectAllCheckbox = document.getElementById('selectAllRecords');
    const checkboxes = document.querySelectorAll('.record-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateSelectedRecordsCount();
}

function updateDataStats(stats = {}) {
    const elements = {
        'totalRecordsCount': stats.total_records || 0,
        'validRecordsCount': stats.total_standard + stats.total_cp || 0,
        'engineeringOrdersCount': 0, // 暂时设为0，实际根据业务逻辑计算
        'productionOrdersCount': stats.total_standard || 0
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = value;
    });
}

function updateBatchProcessingResults(results) {
    updateAttachmentStats();
    
    // 更新统计显示
    const statsElements = {
        'totalRecordsCount': results.total_records,
        'validRecordsCount': results.total_records,
        'engineeringOrdersCount': 0,
        'productionOrdersCount': results.standard_orders
    };
    
    Object.entries(statsElements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = value;
    });
}

function refreshSummaryData() {
    // 这个函数可以调用状态API获取最新统计
    fetch(`${API_ENDPOINTS.processing}/status`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateDataStats(data.data.records);
        }
    })
    .catch(error => {
        console.error('刷新统计数据失败:', error);
    });
}

function viewRecordDetail(recordId) {
    addLogEntry('info', `查看记录详情: ${recordId}`);
    // 这里可以实现记录详情查看功能
}

function exportToExcel() {
    exportSelectedData();
}

function exportToCSV() {
    exportSelectedData();
}

function toggleViewMode() {
    const tableView = document.getElementById('tableView');
    const chartView = document.getElementById('chartView');
    const viewModeText = document.getElementById('viewModeText');
    
    if (tableView.style.display === 'none') {
        tableView.style.display = 'block';
        chartView.style.display = 'none';
        viewModeText.textContent = '切换到图表视图';
    } else {
        tableView.style.display = 'none';
        chartView.style.display = 'block';
        viewModeText.textContent = '切换到表格视图';
        // 这里可以初始化图表
        initializeCharts();
    }
}

function initializeCharts() {
    // 初始化ECharts图表
    addLogEntry('info', '加载图表视图...');
}

function clearFilters() {
    // 清除所有筛选条件
    const filters = ['dateRangeFilter', 'orderTypeFilter', 'productFilter', 'statusFilter'];
    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            if (element.tagName === 'SELECT') {
                element.selectedIndex = 0;
            }
        }
    });
    
    addLogEntry('info', '筛选条件已清除');
    applyFilters();
}

function refreshDataPreview(records = []) {
    const tbody = document.getElementById('dataPreviewTableBody');
    const countElement = document.getElementById('previewRecordCount');
    
    if (countElement) countElement.textContent = records.length;
    
    if (!tbody) return;
    
    if (records.length === 0) {
        tbody.innerHTML = `
            <tr id="noDataRow">
                <td colspan="9" class="text-center text-muted py-4">
                    <i class="fas fa-database fa-2x mb-2 d-block"></i>
                    暂无数据，请先处理Excel附件
                </td>
            </tr>
        `;
        return;
    }
    
    const rows = records.slice(0, 100).map(record => `
        <tr>
            <td><input type="checkbox" class="record-checkbox" value="${record.id}"></td>
            <td>${record.product_code || '-'}</td>
            <td>${record.quantity || '-'}</td>
            <td>${record.delivery_date ? formatDateTime(record.delivery_date) : '-'}</td>
            <td><span class="badge ${record.order_type === 'engineering' ? 'bg-primary' : 'bg-success'}">${record.order_type === 'engineering' ? '工程' : '量产'}</span></td>
            <td>${record.source || '-'}</td>
            <td>${record.processed_time ? formatDateTime(record.processed_time) : '-'}</td>
            <td><span class="badge ${getStatusBadgeClass(record.status)}">${getStatusText(record.status)}</span></td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-info" onclick="viewRecordDetail(${record.id})" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = rows;
}

// 完整的邮箱配置管理功能实现
function editEmailConfig(configId) {
    addLogEntry('info', `编辑邮箱配置 ${configId}`);
    
    // 获取配置详情
    fetch(`${API_ENDPOINTS.emailConfig}/${configId}`)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('获取配置详情响应:', data);
        if (data.success || data.status === 'success') {
            const config = data.data;
            
            // 填充表单数据
            document.getElementById('modalTitle').textContent = '编辑邮箱配置';
            document.getElementById('configId').value = config.id;
            document.getElementById('configName').value = config.name || '';
            document.getElementById('emailAddress').value = config.email || '';
            document.getElementById('emailPassword').value = ''; // 安全考虑，不回填密码
            document.getElementById('imapServer').value = config.server || '';
            document.getElementById('imapPort').value = config.port || 993;
            document.getElementById('senderFilter').value = config.senders || '';
            document.getElementById('subjectKeywords').value = config.subjects || '';
            document.getElementById('checkInterval').value = config.check_interval || 60;
            document.getElementById('workStartTime').value = config.work_start_time || '08:00';
            document.getElementById('workEndTime').value = config.work_end_time || '18:00';
            document.getElementById('downloadPath').value = config.download_path || '';
            document.getElementById('useDateFolder').checked = config.use_date_folder || false;
            document.getElementById('fetchDays').value = config.fetch_days || 10;
            document.getElementById('maxAttachmentSize').value = config.max_attachment_size || 50;
            document.getElementById('allowedExtensions').value = config.allowed_extensions || '.xlsx,.xls,.csv';
            document.getElementById('enableSSL').checked = config.enable_ssl !== false;
            document.getElementById('markAsRead').checked = config.mark_as_read !== false;
            document.getElementById('enableConfig').checked = config.enabled || false;
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('emailConfigModal'));
            modal.show();
        } else {
            addLogEntry('error', '获取邮箱配置详情失败：' + (data.error || data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        console.error('获取配置详情错误:', error);
        addLogEntry('error', '获取邮箱配置详情时发生错误：' + error.message);
    });
}

function deleteEmailConfig(configId) {
    if (!confirm('确认删除此邮箱配置吗？此操作不可撤销。')) {
        return;
    }
    
    addLogEntry('info', `删除邮箱配置 ${configId}`);
    
    fetch(`${API_ENDPOINTS.emailConfig}/${configId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('删除API响应:', data);
        if (data.success || data.status === 'success') {
            addLogEntry('success', '邮箱配置删除成功');
            refreshEmailConfigs();
        } else {
            addLogEntry('error', '删除邮箱配置失败：' + (data.error || data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        console.error('删除配置错误:', error);
        addLogEntry('error', '删除邮箱配置时发生错误：' + error.message);
    });
}

function processAttachment(attachmentId) {
    addLogEntry('info', `处理附件 ${attachmentId}`);
    
    fetch(`/api/email_attachments/${attachmentId}/process`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('success', '附件处理任务已启动');
            refreshAttachmentsList();
            refreshSummaryData();
        } else {
            addLogEntry('error', '附件处理失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '处理附件时发生错误：' + error.message);
    });
}

function viewRecordDetail(recordId) {
    addLogEntry('info', `查看记录详情 ${recordId}`);
    
    fetch(`${API_ENDPOINTS.processing}/${recordId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示记录详情模态框（需要添加模态框HTML）
            console.log('记录详情:', data.data);
            addLogEntry('info', '记录详情已加载');
        } else {
            addLogEntry('error', '获取记录详情失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '获取记录详情时发生错误：' + error.message);
    });
}

function toggleAllAttachments() {
    const checkboxes = document.querySelectorAll('.attachment-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllAttachments');
    const isChecked = selectAllCheckbox ? selectAllCheckbox.checked : false;
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
    });
    
    addLogEntry('info', `${isChecked ? '全选' : '取消全选'}附件`);
}

function toggleAllRecords() {
    const checkboxes = document.querySelectorAll('.record-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllRecords');
    const isChecked = selectAllCheckbox ? selectAllCheckbox.checked : false;
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
    });
    
    addLogEntry('info', `${isChecked ? '全选' : '取消全选'}记录`);
}

function processSelectedAttachments() {
    const selectedAttachments = Array.from(document.querySelectorAll('.attachment-checkbox:checked')).map(cb => cb.value);
    
    if (selectedAttachments.length === 0) {
        addLogEntry('warning', '请先选择要处理的附件');
        return;
    }
    
    addLogEntry('info', `批量处理 ${selectedAttachments.length} 个附件`);
    
    fetch('/api/email_attachments/batch_process', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            attachment_ids: selectedAttachments.map(id => parseInt(id))
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('success', '批量处理任务已启动');
            refreshAttachmentsList();
            refreshSummaryData();
        } else {
            addLogEntry('error', '批量处理失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '批量处理时发生错误：' + error.message);
    });
}

function deleteSelectedAttachments() {
    const selectedAttachments = Array.from(document.querySelectorAll('.attachment-checkbox:checked')).map(cb => cb.value);
    
    if (selectedAttachments.length === 0) {
        addLogEntry('warning', '请先选择要删除的附件');
        return;
    }
    
    if (!confirm(`确认删除选中的 ${selectedAttachments.length} 个附件吗？此操作不可撤销。`)) {
        return;
    }
    
    addLogEntry('info', `批量删除 ${selectedAttachments.length} 个附件`);
    // 实现批量删除逻辑
}

function generateEngineeringReport() {
    addLogEntry('info', '生成工程订单报表');
    
    fetch('/api/order_data/download_summary?type=engineering')
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('生成报表失败');
    })
    .then(blob => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `工程订单汇总_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
        addLogEntry('success', '工程订单报表已生成并下载');
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '生成工程订单报表失败：' + error.message);
    });
}

function generateProductionReport() {
    addLogEntry('info', '生成量产订单报表');
    
    fetch('/api/order_data/download_summary?type=production')
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('生成报表失败');
    })
    .then(blob => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `量产订单汇总_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
        addLogEntry('success', '量产订单报表已生成并下载');
    })
    .catch(error => {
        console.error('API请求失败:', error);
        addLogEntry('error', '生成量产订单报表失败：' + error.message);
    });
}

function toggleViewMode() {
    const dataPreviewCard = document.querySelector('#step3 .data-preview-card');
    if (dataPreviewCard) {
        dataPreviewCard.classList.toggle('fullscreen-mode');
        addLogEntry('info', '切换视图模式');
    }
}

function exportToExcel() {
    addLogEntry('info', '导出到Excel');
    window.open('/api/order_data/export?format=excel', '_blank');
}

function exportToCSV() {
    addLogEntry('info', '导出到CSV');
    window.open('/api/order_data/export?format=csv', '_blank');
}

function exportToPDF() {
    addLogEntry('info', '导出到PDF');
    window.open('/api/order_data/export?format=pdf', '_blank');
}

// 添加缺失的函数
function resetWorkflow() {
    // 重置工作流状态
    currentStep = 1;
    switchStep(1);
    
    // 重置进度
    updateTaskProgress({ 
        overall: 0, 
        step: 0, 
        message: '工作流已重置，等待开始...' 
    });
    
    // 清空选择状态
    document.querySelectorAll('.attachment-checkbox').forEach(cb => cb.checked = false);
    document.querySelectorAll('.record-checkbox').forEach(cb => cb.checked = false);
    
    addLogEntry('info', '工作流已重置');
}

function refreshStatus() {
    // 刷新所有状态信息
    addLogEntry('info', '正在刷新状态...');
    
    // 刷新连接状态
    updateConnectionStatus('connected');
    
    // 刷新数据
    loadInitialData();
    
    addLogEntry('success', '状态刷新完成');
}

function resetProcessingState() {
    // 重置处理状态
    isProcessing = false;
    currentTaskId = null;
    processingStatus = 'idle';
    
    // 更新UI状态
    const startButton = document.querySelector('[onclick*="startAttachmentProcessing"]');
    if (startButton) {
        startButton.disabled = false;
        startButton.innerHTML = '<i class="fas fa-play me-1"></i>开始处理';
    }
}

function updateConnectionStatus(status) {
    const statusElement = document.getElementById('connectionStatus');
    const statusIcon = document.getElementById('connectionIcon');
    
    if (statusElement && statusIcon) {
        const statusConfig = {
            'connecting': { text: '连接中', icon: 'fa-circle-notch fa-spin', class: 'text-warning' },
            'connected': { text: '已连接', icon: 'fa-check-circle', class: 'text-success' },
            'disconnected': { text: '已断开', icon: 'fa-times-circle', class: 'text-danger' },
            'error': { text: '连接错误', icon: 'fa-exclamation-circle', class: 'text-danger' }
        };
        
        const config = statusConfig[status] || statusConfig['disconnected'];
        statusElement.textContent = config.text;
        statusElement.className = config.class;
        statusIcon.className = `fas ${config.icon} me-1`;
    }
}

// 重复的switchStep函数已删除，使用第一个更完整的版本

// 重复定义已删除，使用第一组更完整的函数定义

// ===== 缺失的核心功能函数 =====

function startAttachmentProcessing() {
    addLogEntry('info', '开始自动处理任务...');
    
    const startButton = document.querySelector('[onclick*="startAttachmentProcessing"]');
    if (startButton) {
        startButton.disabled = true;
        startButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
    }
    
    // 首先扫描附件
    fetch(`${API_ENDPOINTS.attachments}/scan`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            attachmentsList = data.data || [];
            updateAttachmentsList(attachmentsList);
            updateAttachmentStats();
            
            addLogEntry('success', `扫描完成：发现 ${attachmentsList.length} 个Excel文件`);
            
            // 如果有文件，自动开始批量处理
            if (attachmentsList.length > 0) {
                processBatchAttachments();
            } else {
                addLogEntry('warning', '没有找到Excel文件需要处理');
            }
        } else {
            addLogEntry('error', '扫描失败：' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('扫描请求失败:', error);
        addLogEntry('error', '启动处理任务时发生错误：' + error.message);
    })
    .finally(() => {
        if (startButton) {
            startButton.disabled = false;
            startButton.innerHTML = '<i class="fas fa-play me-1"></i>开始处理';
        }
    });
}

function refreshAttachmentsList() {
    addLogEntry('info', '刷新附件列表...');
    scanAttachments();
}

function updateAttachmentStats() {
    // 更新附件统计信息
    const totalCount = attachmentsList.length;
    const processedCount = attachmentsList.filter(att => att.processed === true).length;
    const pendingCount = totalCount - processedCount;
    const errorCount = attachmentsList.filter(att => att.status === 'error').length;
    
    document.getElementById('totalAttachmentsCount').textContent = totalCount;
    document.getElementById('processedCount').textContent = processedCount;
    document.getElementById('pendingCount').textContent = pendingCount;
    document.getElementById('errorCount').textContent = errorCount;
    
    // 更新成功率
    const successRate = totalCount > 0 ? Math.round((processedCount / totalCount) * 100) : 0;
    const successRateElement = document.getElementById('successRate');
    const successRateBarElement = document.getElementById('successRateBar');
    if (successRateElement) successRateElement.textContent = `${successRate}%`;
    if (successRateBarElement) successRateBarElement.style.width = `${successRate}%`;
    
    // 同时更新快速统计面板
            // 更新实时统计功能已移除
}

// updateQuickStats 函数已删除（实时统计功能已移除）

function updateBatchProcessingResults(results) {
    // 更新批量处理结果统计
    updateAttachmentStats();
    
    // 更新最后处理时间
    document.getElementById('lastProcessTime').textContent = new Date().toLocaleString();
    
    // 计算平均处理时间（示例）
    const avgTime = results.total_files > 0 ? Math.round(results.processing_time / results.total_files * 1000) : 0;
    document.getElementById('avgProcessTime').textContent = `${avgTime}ms/文件`;
}

function refreshSummaryData() {
    // 刷新汇总数据
    addLogEntry('info', '刷新汇总数据...');
    refreshDataPreview();
}

function updateDataStats(stats = {}) {
    // 更新数据统计信息
    document.getElementById('totalRecordsCount').textContent = stats.total_records || 0;
    document.getElementById('standardOrdersCount').textContent = stats.standard_orders || 0;
    document.getElementById('cpOrdersCount').textContent = stats.cp_orders || 0;
    document.getElementById('uniqueChipsCount').textContent = stats.unique_chips || 0;
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，开始初始化...');
    
    // 等待Socket.IO库加载完成
    setTimeout(() => {
        console.log('Socket.IO库状态:', typeof io !== 'undefined' ? '已加载' : '未加载');
        initializeConsole();
        loadInitialData();
    }, 500);
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    if (websocketConnection && websocketConnection.disconnect) {
        websocketConnection.disconnect();
    }
});

// 新增：从汇总表加载数据的函数
function loadSummaryData(tableType = 'ft_summary') {
    addLogEntry('info', `加载${tableType === 'cp_summary' ? 'CP' : 'FT'}订单汇总数据...`);
    
    fetch(`${API_ENDPOINTS.data}/preview?table_type=${tableType}&limit=50`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateDataPreviewTable(data.data || []);
            
            // 更新统计信息
            const stats = data.stats;
            if (stats) {
                document.getElementById('previewRecordCount').textContent = stats.current_count || 0;
                
                // 更新总计信息
                if (stats.summary_stats) {
                    const summaryStats = stats.summary_stats;
                    updateDataStatsFromSummary(summaryStats);
                }
            }
            
            addLogEntry('success', `成功加载 ${data.data.length} 条${stats.table_name}记录`);
        } else {
            addLogEntry('error', `加载汇总数据失败: ${data.message}`);
        }
    })
    .catch(error => {
        console.error('加载汇总数据失败:', error);
        addLogEntry('error', `网络错误: ${error.message}`);
    });
}

// 新增：更新统计信息的函数
function updateDataStatsFromSummary(summaryStats) {
    const elements = {
        'totalRecordsCount': summaryStats.total_count || 0,
        'validRecordsCount': summaryStats.ft_order_count + summaryStats.cp_order_count || 0,
        'engineeringOrdersCount': summaryStats.cp_order_count || 0,
        'productionOrdersCount': summaryStats.ft_order_count || 0
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = value;
    });
}

// 新增：表格类型切换函数
function switchTableType(tableType) {
    addLogEntry('info', `切换到${tableType === 'cp_summary' ? 'CP' : 'FT'}订单汇总表`);
    loadSummaryData(tableType);
}

// 新增：获取当前表格类型
function getCurrentTableType() {
    const checkedRadio = document.querySelector('input[name="tableType"]:checked');
    return checkedRadio ? checkedRadio.value : 'ft_summary';
}

// 修改：页面初始化时加载汇总数据
document.addEventListener('DOMContentLoaded', function() {
    connectWebSocket();
    updateConnectionStatus('connecting');
    
    // 初始化加载FT订单汇总数据
    loadSummaryData('ft_summary');
    
    addLogEntry('info', '订单处理控制台已初始化');
    addLogEntry('info', 'WebSocket连接准备就绪');
    addLogEntry('success', '系统服务检查完成，所有服务正常');
});

// 步骤3专用JavaScript函数
let currentOrderType = 'ft_summary';
let currentOrderData = [];
let currentOrderPage = 1;
let totalOrderPages = 1;

// 订单类型切换
function switchOrderType(orderType) {
    currentOrderType = orderType;
    currentOrderPage = 1;
    
    // 重新加载数据
    loadOrderData(orderType);
    
    addLogEntry('info', `切换到${orderType === 'ft_summary' ? 'FT' : 'CP'}订单数据`);
}

// 加载订单数据
function loadOrderData(orderType = 'ft_summary', page = 1, pageSize = 50, filters = {}) {
    showOrderLoading(true);

    const url = orderType === 'ft_summary' ? '/api/v2/orders/data/ft_summary' : '/api/v2/orders/data/cp_summary';
    const params = new URLSearchParams({
        page: page,
        page_size: pageSize,
        ...filters
    });

    fetch(`${url}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentOrderData = data.data || [];
                renderOrderTable(currentOrderData, orderType);
                updateOrderPagination(data.pagination || {});
                updateOrderStatistics(data.statistics || {});
            } else {
                addLogEntry('error', '加载订单数据失败: ' + (data.message || '未知错误'));
                renderOrderTable([], orderType);
            }
        })
        .catch(error => {
            console.error('加载订单数据错误:', error);
            addLogEntry('error', '加载订单数据失败: ' + error.message);
            renderOrderTable([], orderType);
        })
        .finally(() => {
            showOrderLoading(false);
        });
}

// 渲染订单表格
function renderOrderTable(data, orderType) {
    const tableHeaders = document.getElementById('orderTableHeaders');
    const tableBody = document.getElementById('orderTableBody');
    
    if (!tableHeaders || !tableBody) return;
    
    // 生成表头
    let headers = '<th class="select-column"><input type="checkbox" id="selectAllOrders" onchange="toggleAllOrderRecords()"></th>';
    
    if (orderType === 'ft_summary') {
        headers += `
            <th>订单号</th>
            <th>下单日期</th>
            <th>标签名称</th>
            <th>电路名称</th>
            <th>芯片名称</th>
            <th>晶圆尺寸</th>
            <th>封装数量1</th>
            <th>封装数量2</th>
            <th>扩散批次</th>
            <th>晶圆编号</th>
            <th>交期</th>
            <th>分类结果</th>
            <th class="action-column">操作</th>
        `;
    } else {
        headers += `
            <th>订单号</th>
            <th>产品名称</th>
            <th>芯片名称</th>
            <th>加工属性</th>
            <th>加工承揽商</th>
            <th>加工片数</th>
            <th>成品型号</th>
            <th>工序</th>
            <th class="action-column">操作</th>
        `;
    }
    
    tableHeaders.innerHTML = headers;
    
    // 生成表格内容
    if (data && data.length > 0) {
        let rows = '';
        data.forEach(row => {
            rows += renderOrderRow(row, orderType);
        });
        tableBody.innerHTML = rows;
    } else {
        tableBody.innerHTML = `
            <tr>
                <td colspan="20" class="text-center py-4">
                    <i class="fas fa-database fa-2x mb-2 d-block text-muted"></i>
                    <span class="text-muted">暂无数据</span>
                </td>
            </tr>
        `;
    }
}

// 渲染单行数据
function renderOrderRow(row, orderType) {
    const rowId = row.id;
    let content = `<tr data-id="${rowId}">`;
    
    content += `<td class="select-column"><input type="checkbox" class="row-select" value="${rowId}" onchange="updateOrderSelection()"></td>`;
    
    if (orderType === 'ft_summary') {
        content += `
            <td title="${row.order_number || ''}">${truncateText(row.order_number || '', 15)}</td>
            <td>${row.order_date || ''}</td>
            <td title="${row.label_name || ''}">${truncateText(row.label_name || '', 20)}</td>
            <td title="${row.circuit_name || ''}">${truncateText(row.circuit_name || '', 15)}</td>
            <td title="${row.chip_name || ''}">${truncateText(row.chip_name || '', 15)}</td>
            <td>${row.wafer_size || ''}</td>
            <td>${row.package_qty1 || ''}</td>
            <td>${row.package_qty2 || ''}</td>
            <td title="${row.diffusion_batch || ''}">${truncateText(row.diffusion_batch || '', 10)}</td>
            <td title="${row.wafer_id || ''}">${truncateText(row.wafer_id || '', 10)}</td>
            <td>${row.delivery_date || ''}</td>
            <td><span class="badge ${row.classification_result === '量产' ? 'bg-success' : 'bg-info'}">${row.classification_result || ''}</span></td>
        `;
    } else {
        content += `
            <td title="${row.order_number || ''}">${truncateText(row.order_number || '', 15)}</td>
            <td title="${row.product_name || ''}">${truncateText(row.product_name || '', 20)}</td>
            <td title="${row.chip_name || ''}">${truncateText(row.chip_name || '', 15)}</td>
            <td>${row.processing_type || ''}</td>
            <td title="${row.contractor_name || ''}">${truncateText(row.contractor_name || '', 15)}</td>
            <td>${row.processing_pieces || ''}</td>
            <td title="${row.finished_model || ''}">${truncateText(row.finished_model || '', 15)}</td>
            <td>${row.process_step || ''}</td>
        `;
    }
    
    content += `
        <td class="action-column">
            <button type="button" class="btn btn-sm btn-outline-primary me-1" onclick="editOrder(${rowId}, '${orderType}')" title="编辑">
                <i class="fas fa-edit"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-info me-1" onclick="viewOrder(${rowId}, '${orderType}')" title="查看">
                <i class="fas fa-eye"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteOrder(${rowId}, '${orderType}')" title="删除">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    </tr>`;
    
    return content;
}

// 辅助函数：截断文本
function truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// 显示/隐藏加载状态
function showOrderLoading(show) {
    const overlay = document.getElementById('orderLoadingOverlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

// 快速筛选
function quickOrderFilter(field, value) {
    const filterField = document.getElementById('orderFilterField');
    const filterValue = document.getElementById('orderFilterValue');
    const filterOperator = document.getElementById('orderFilterOperator');
    
    if (filterField && filterValue && filterOperator) {
        filterField.value = field;
        
        if (value === 'today') {
            const today = new Date().toISOString().split('T')[0];
            filterValue.value = today;
            filterOperator.value = 'equals';
        } else if (value === 'urgent') {
            const urgentDate = new Date();
            urgentDate.setDate(urgentDate.getDate() + 7);
            filterValue.value = urgentDate.toISOString().split('T')[0];
            filterOperator.value = 'less_than';
        } else {
            filterValue.value = value;
            filterOperator.value = 'equals';
        }
        
        applyOrderFilter();
    }
}

// 应用订单筛选
function applyOrderFilter() {
    const field = document.getElementById('orderFilterField').value;
    const operator = document.getElementById('orderFilterOperator').value;
    const value = document.getElementById('orderFilterValue').value;
    
    if (!field || !value) {
        addLogEntry('warning', '请选择筛选字段和输入筛选值');
        return;
    }
    
    const filters = {
        field: field,
        operator: operator,
        value: value
    };
    
    loadOrderData(currentOrderType, 1, getCurrentOrderPageSize(), filters);
    addLogEntry('info', `已应用筛选条件: ${field} ${operator} ${value}`);
}

// 清除订单筛选
function clearOrderFilter() {
    document.getElementById('orderFilterField').value = '';
    document.getElementById('orderFilterValue').value = '';
    document.getElementById('orderFilterOperator').value = 'contains';
    
    loadOrderData(currentOrderType);
    addLogEntry('info', '已清除筛选条件');
}

// 获取当前页面大小
function getCurrentOrderPageSize() {
    const pageSizeSelect = document.getElementById('orderPageSize');
    return pageSizeSelect ? pageSizeSelect.value : '50';
}

// 更新订单统计信息
function updateOrderStatistics(statistics) {
    document.getElementById('totalOrderRecords').textContent = statistics.total || '--';
    document.getElementById('validOrderRecords').textContent = statistics.valid || '--';
    document.getElementById('engineeringOrderCount').textContent = statistics.engineering || '--';
    document.getElementById('productionOrderCount').textContent = statistics.production || '--';
}

// 订单选择相关函数
function toggleAllOrderRecords() {
    const selectAll = document.getElementById('selectAllOrders');
    const checkboxes = document.querySelectorAll('.row-select');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateOrderSelection();
}

function updateOrderSelection() {
    const selectedCheckboxes = document.querySelectorAll('.row-select:checked');
    const selectedCount = selectedCheckboxes.length;
    
    // 更新选择计数
    const countElement = document.getElementById('orderSelectedCount');
    if (countElement) {
        countElement.textContent = selectedCount;
    }
    
    // 显示/隐藏批量操作栏
    const batchOperations = document.getElementById('orderBatchOperations');
    if (batchOperations) {
        batchOperations.style.display = selectedCount > 0 ? 'block' : 'none';
    }
}

// 刷新订单数据
function refreshOrderData() {
    addLogEntry('info', '开始刷新订单数据...');
    loadOrderData(currentOrderType);
}

// 导出功能
function exportOrdersToExcel() {
    const selectedIds = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        addLogEntry('warning', '请先选择要导出的记录');
        return;
    }
    
    exportSelectedOrderData();
}

function exportSelectedOrderData() {
    const selectedIds = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);

    if (selectedIds.length === 0) {
        addLogEntry('warning', '请先选择要导出的记录');
        return;
    }

    addLogEntry('info', `开始导出${selectedIds.length}条${currentOrderType === 'ft_summary' ? 'FT' : 'CP'}订单记录...`);

    // 使用POST请求导出数据
    const exportData = {
        order_type: currentOrderType === 'ft_summary' ? 'FT' : 'CP',
        format: 'excel',
        selected_ids: selectedIds.map(id => parseInt(id))
    };

    fetch('/api/v2/orders/data/export', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(exportData)
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        } else {
            throw new Error(`导出失败: ${response.status} ${response.statusText}`);
        }
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = `${currentOrderType}_orders_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        window.URL.revokeObjectURL(url);

        addLogEntry('success', `导出完成：${selectedIds.length}条记录`);
    })
    .catch(error => {
        console.error('导出失败:', error);
        addLogEntry('error', '导出失败: ' + error.message);
    });
}

// 分页功能
function updateOrderPagination(pagination) {
    currentOrderPage = pagination.current_page || 1;
    totalOrderPages = pagination.total_pages || 1;
    
    // 更新记录信息
    document.getElementById('orderCurrentStart').textContent = pagination.start || 0;
    document.getElementById('orderCurrentEnd').textContent = pagination.end || 0;
    document.getElementById('orderTotalRecords').textContent = pagination.total || 0;
    
    // 更新分页按钮
    const paginationElement = document.getElementById('orderPagination');
    if (paginationElement && totalOrderPages > 1) {
        let paginationHTML = '';
        
        // 上一页
        paginationHTML += `
            <li class="page-item ${currentOrderPage <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadOrderData(currentOrderType, ${currentOrderPage - 1})">上一页</a>
            </li>
        `;
        
        // 页码
        for (let i = 1; i <= totalOrderPages; i++) {
            if (i === currentOrderPage || i === 1 || i === totalOrderPages || Math.abs(i - currentOrderPage) <= 2) {
                paginationHTML += `
                    <li class="page-item ${i === currentOrderPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadOrderData(currentOrderType, ${i})">${i}</a>
                    </li>
                `;
            } else if (i === currentOrderPage - 3 || i === currentOrderPage + 3) {
                paginationHTML += '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
            }
        }
        
        // 下一页
        paginationHTML += `
            <li class="page-item ${currentOrderPage >= totalOrderPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadOrderData(currentOrderType, ${currentOrderPage + 1})">下一页</a>
            </li>
        `;
        
        paginationElement.innerHTML = paginationHTML;
    }
}

// 页面大小变更
function changeOrderPageSize() {
    const pageSize = getCurrentOrderPageSize();
    loadOrderData(currentOrderType, 1, pageSize);
    addLogEntry('info', `页面大小已更改为${pageSize}条/页`);
}

// 编辑订单
function editOrder(id, orderType) {
    addLogEntry('info', `编辑${orderType === 'ft_summary' ? 'FT' : 'CP'}订单: ${id}`);
    // TODO: 实现编辑功能
}

// 查看订单
function viewOrder(id, orderType) {
    addLogEntry('info', `查看${orderType === 'ft_summary' ? 'FT' : 'CP'}订单: ${id}`);
    // TODO: 实现查看功能
}

// 删除订单
function deleteOrder(id, orderType) {
    if (confirm('确定要删除这条订单记录吗？')) {
        addLogEntry('info', `删除${orderType === 'ft_summary' ? 'FT' : 'CP'}订单: ${id}`);
        // TODO: 实现删除功能
    }
}

// 批量操作
function batchExportOrders() {
    exportSelectedOrderData();
}

function batchEditOrders() {
    const selectedIds = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
    addLogEntry('info', `批量编辑${selectedIds.length}条记录`);
    // TODO: 实现批量编辑功能
}

function batchDeleteOrders() {
    const selectedIds = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
    if (selectedIds.length > 0 && confirm(`确定要删除选中的${selectedIds.length}条记录吗？`)) {
        addLogEntry('info', `批量删除${selectedIds.length}条记录`);
        // TODO: 实现批量删除功能
    }
}

function clearOrderSelection() {
    document.querySelectorAll('.row-select').forEach(cb => cb.checked = false);
    document.getElementById('selectAllOrders').checked = false;
    updateOrderSelection();
}

// 初始化步骤3
function initializeStep3() {
    // 加载初始数据
    loadOrderData('ft_summary');
    
    addLogEntry('info', '步骤3：订单数据管理已初始化');
}

// ===== 调试测试函数 =====
function debugStepSwitch() {
    console.log('🔧 开始调试步骤切换功能...');
    
    // 检查当前状态
    console.log('📊 当前步骤:', currentStep);
    console.log('📊 步骤元素:', document.querySelectorAll('.step-item'));
    console.log('📊 面板元素:', document.querySelectorAll('.step-panel'));
    
    // 检查每个步骤按钮
    document.querySelectorAll('.step-item').forEach((item, index) => {
        const stepNum = index + 1;
        console.log(`步骤 ${stepNum}:`, {
            element: item,
            classes: Array.from(item.classList),
            onclick: item.getAttribute('onclick'),
            dataStep: item.getAttribute('data-step')
        });
    });
    
    // 检查每个面板
    document.querySelectorAll('.step-panel').forEach((panel, index) => {
        const panelNum = index + 1;
        console.log(`面板 ${panelNum}:`, {
            element: panel,
            id: panel.id,
            classes: Array.from(panel.classList),
            display: window.getComputedStyle(panel).display
        });
    });
}

function testStepSwitch() {
    console.log('🧪 测试步骤切换...');
    
    console.log('测试切换到步骤1...');
    switchStep(1);
    
    setTimeout(() => {
        console.log('测试切换到步骤2...');
        switchStep(2);
        
        setTimeout(() => {
            console.log('测试切换到步骤3...');
            switchStep(3);
        }, 1000);
    }, 1000);
}

// 在页面加载完成后自动运行调试
document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 页面加载完成，准备调试...');
    
    // 延迟执行调试，确保所有元素都已加载
    setTimeout(() => {
        debugStepSwitch();
    }, 500);
});

// switchStep函数已在前面定义，不需要重复定义

// ===== 全局错误处理和调试 =====
window.addEventListener('error', function(e) {
    console.error('🔴 JavaScript错误:', e.error);
    console.error('🔴 错误详情:', {
        message: e.message,
        filename: e.filename,
        lineno: e.lineno,
        colno: e.colno,
        stack: e.error ? e.error.stack : 'No stack'
    });
    addLogEntry('error', 'JavaScript错误: ' + e.message);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('🔴 Promise错误:', e.reason);
    addLogEntry('error', 'Promise错误: ' + e.reason);
});

// 页面加载完成后的全面检查
document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 页面DOM加载完成，开始检查...');
    
    // 检查关键函数
    console.log('🔍 检查关键函数...');
    const functions = ['startProcessing', 'pauseProcessing', 'stopProcessing', 'updateTaskProgress', 'resetProcessingState'];
    functions.forEach(func => {
        if (typeof window[func] === 'function') {
            console.log('✅ 函数存在:', func);
        } else {
            console.error('❌ 函数缺失:', func);
        }
    });
    
    // 检查关键元素
    console.log('🔍 检查关键UI元素...');
    const elements = ['startBtn', 'pauseBtn', 'stopBtn', 'taskStatus', 'logContainer'];
    elements.forEach(id => {
        const elem = document.getElementById(id);
        if (elem) {
            console.log('✅ 元素存在:', id, elem);
        } else {
            console.error('❌ 元素缺失:', id);
        }
    });
    
    // 检查API_ENDPOINTS
    console.log('🔍 检查API配置...');
    if (typeof API_ENDPOINTS !== 'undefined') {
        console.log('✅ API_ENDPOINTS存在:', API_ENDPOINTS);
    } else {
        console.error('❌ API_ENDPOINTS未定义');
    }
    
    // 检查emailConfigs
    console.log('🔍 检查邮箱配置...');
    if (typeof emailConfigs !== 'undefined') {
        console.log('✅ emailConfigs存在，数量:', emailConfigs.length);
        console.log('📧 邮箱配置详情:', emailConfigs);
    } else {
        console.error('❌ emailConfigs未定义');
    }
    
    // 检查按钮事件绑定
    console.log('🔍 检查按钮事件绑定...');
    const startBtn = document.getElementById('startBtn');
    if (startBtn) {
        const onclick = startBtn.getAttribute('onclick');
        console.log('✅ 自动按钮onclick属性:', onclick);
        
        // 添加额外的事件监听器作为备份
        startBtn.addEventListener('click', function(e) {
            console.log('🎯 自动按钮被点击 (addEventListener)');
            if (!onclick || !onclick.includes('startProcessing')) {
                console.log('🔄 使用备用事件处理');
                startProcessing('auto');
            }
        });
    }
    
    console.log('🎉 页面检查完成！');
});

// 添加按钮点击确认
function confirmButtonClick(buttonName, func) {
    console.log(`🎯 ${buttonName}按钮被点击`);
    return func();
}

// 重写按钮函数以添加确认
if (typeof startProcessing === 'function') {
    const originalStartProcessing = startProcessing;
    window.startProcessing = function(mode) {
        return confirmButtonClick('自动处理', () => originalStartProcessing(mode));
    };
}



// ===== 授权码保存功能 =====
function saveAuthCode(configId, authCode) {
    console.log('💾 保存授权码，配置ID:', configId);
    
    return fetch(`/api/v2/orders/email-configs/${configId}/save-auth`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            auth_code: authCode
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ 授权码保存成功');
            addLogEntry('success', '授权码已保存，下次将自动填入');
            return true;
        } else {
            console.error('❌ 授权码保存失败:', data.error);
            addLogEntry('error', '授权码保存失败: ' + data.error);
            return false;
        }
    })
    .catch(error => {
        console.error('❌ 保存授权码请求失败:', error);
        addLogEntry('error', '保存授权码时发生错误: ' + error.message);
        return false;
    });
}

function loadSavedAuthCode(configId) {
    console.log('📥 加载保存的授权码，配置ID:', configId);
    
    return fetch(`/api/v2/orders/email-configs/${configId}/get-auth`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ 授权码加载成功');
            return data.auth_code;
        } else {
            console.log('ℹ️ 未找到保存的授权码');
            return null;
        }
    })
    .catch(error => {
        console.error('❌ 加载授权码失败:', error);
        return null;
    });
}

// 在邮箱配置表单中自动填入保存的授权码
function autoFillAuthCode() {
    const configSelect = document.getElementById('testEmailSelect');
    const passwordField = document.getElementById('emailPassword');
    
    if (configSelect && passwordField && configSelect.value) {
        loadSavedAuthCode(configSelect.value).then(authCode => {
            if (authCode) {
                passwordField.value = authCode;
                addLogEntry('info', '已自动填入保存的授权码');
            }
        });
    }
}

// 邮箱测试时自动保存授权码
function enhanceEmailTesting() {
    const originalTestFunction = window.testEmailConnection;
    if (typeof originalTestFunction === 'function') {
        window.testEmailConnection = function() {
            const configSelect = document.getElementById('testEmailSelect');
            const passwordField = document.getElementById('emailPassword');
            
            if (configSelect && passwordField && configSelect.value && passwordField.value) {
                // 先保存授权码
                saveAuthCode(configSelect.value, passwordField.value);
            }
            
            return originalTestFunction();
        };
    }
}

// 页面加载时初始化授权码功能
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        enhanceEmailTesting();
        
        // 监听邮箱配置选择变化
        const configSelect = document.getElementById('testEmailSelect');
        if (configSelect) {
            configSelect.addEventListener('change', autoFillAuthCode);
        }
    }, 1500);
});

</script>
{% endblock %}

