from flask import jsonify, request, Blueprint, send_file
from flask_login import login_required, current_user
import os
import datetime
import json
from app import db
from app.models import EmailConfig, ExcelMapping, EmailAttachment, OrderData
from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
from app.services.enhanced_excel_parser import EnhancedExcelParser
from app.decorators import admin_required
import logging
import pandas as pd
import io
import glob
import time
import shutil
from werkzeug.utils import secure_filename

# 创建蓝图
email_attachment_bp = Blueprint('email_attachment', __name__)

# 创建自定义日志处理器来捕获最近的错误信息
class ErrorLogHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.error_log = []
        
    def emit(self, record):
        if record.levelno >= logging.ERROR:
            self.error_log.append(self.format(record))
            # 仅保留最近的10条错误信息
            if len(self.error_log) > 10:
                self.error_log.pop(0)
    
    def get_last_error(self):
        if self.error_log:
            return self.error_log[-1]
        return None
    
    def clear(self):
        self.error_log = []

# 创建和注册日志处理器
error_handler = ErrorLogHandler()
error_handler.setFormatter(logging.Formatter('%(message)s'))
logger = logging.getLogger('app.utils.email_processor')
logger.addHandler(error_handler)

@email_attachment_bp.route('/api/email_configs', methods=['GET'])
@login_required
def get_email_configs():
    """获取所有邮箱配置"""
    configs = EmailConfig.query.all()
    return jsonify({
        'status': 'success',
        'data': [config.to_dict() for config in configs]
    })

@email_attachment_bp.route('/api/email_configs/<int:config_id>', methods=['GET'])
@login_required
def get_email_config(config_id):
    """获取指定邮箱配置"""
    config = EmailConfig.query.get_or_404(config_id)
    return jsonify({
        'status': 'success',
        'data': config.to_dict()
    })

@email_attachment_bp.route('/api/email_configs', methods=['POST'])
@login_required
def create_email_config():
    """创建邮箱配置"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'email', 'server', 'port', 'password', 'download_path']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'status': 'error',
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查邮箱名称是否已存在
        if EmailConfig.query.filter_by(name=data['name']).first():
            return jsonify({
                'status': 'error',
                'message': f'配置名称 "{data["name"]}" 已存在，请使用其他名称'
            }), 400
        
        # 检查邮箱地址是否已存在
        if EmailConfig.query.filter_by(email=data['email']).first():
            return jsonify({
                'status': 'error',
                'message': f'邮箱地址 "{data["email"]}" 已配置，请使用其他邮箱地址'
            }), 400
        
        # 创建配置
        config = EmailConfig(
            name=data.get('name'),
            server=data.get('server', 'imap.qiye.163.com'),
            port=int(data.get('port', 993)),  # 确保转换为整数
            email=data.get('email'),
            password=data.get('password'),
            senders=data.get('senders'),
            subjects=data.get('subjects'),
            check_interval=int(data.get('check_interval', 60)),  # 确保转换为整数
            work_start_time=data.get('work_start_time', '08:00'),
            work_end_time=data.get('work_end_time', '18:00'),
            enabled=bool(data.get('enabled', False)),  # 确保转换为布尔值
            download_path=data.get('download_path', 'downloads/email_attachments'),
            use_date_folder=bool(data.get('use_date_folder', True)),  # 确保转换为布尔值
            fetch_days=int(data.get('fetch_days', 10)),  # 添加抓取天数设置
            created_by=current_user.username
        )
        
        # 尝试创建目录
        try:
            os.makedirs(config.download_path, exist_ok=True)
        except OSError as e:
            return jsonify({
                'status': 'error',
                'message': f'无法创建附件保存目录: {str(e)}'
            }), 400
        
        db.session.add(config)
        db.session.commit()
        
        logger.info(f'邮箱配置 "{config.name}" 创建成功')
        
        # 如果配置已启用，刷新调度器
        if config.enabled:
            # 导入调度器
            from app import scheduler
            if scheduler:
                scheduler.add_config(config)
                logger.info(f'已将邮箱配置 "{config.name}" 添加到调度器')
        
        return jsonify({
            'status': 'success',
            'message': '邮箱配置创建成功',
            'data': config.to_dict()
        })
    except ValueError as e:
        logger.error(f'数据格式错误: {str(e)}')
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'数据格式错误: {str(e)}'
        }), 400
    except Exception as e:
        logger.error(f'创建邮箱配置出错: {str(e)}')
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'创建邮箱配置失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/email_configs/<int:config_id>', methods=['PUT'])
@login_required
def update_email_config(config_id):
    """更新邮箱配置"""
    try:
        config = EmailConfig.query.get_or_404(config_id)
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'email', 'server', 'port', 'download_path']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'status': 'error',
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查名称是否与其他配置冲突
        name_conflict = EmailConfig.query.filter(
            EmailConfig.name == data.get('name'),
            EmailConfig.id != config_id
        ).first()
        if name_conflict:
            return jsonify({
                'status': 'error',
                'message': f'配置名称 "{data["name"]}" 已被使用，请使用其他名称'
            }), 400
        
        # 检查邮箱地址是否与其他配置冲突
        email_conflict = EmailConfig.query.filter(
            EmailConfig.email == data.get('email'),
            EmailConfig.id != config_id
        ).first()
        if email_conflict:
            return jsonify({
                'status': 'error',
                'message': f'邮箱地址 "{data["email"]}" 已被配置，请使用其他邮箱地址'
            }), 400
        
        # 保存旧状态以判断是否需要刷新调度器
        old_enabled = config.enabled
        
        # 更新字段
        config.name = data.get('name', config.name)
        config.server = data.get('server', config.server)
        config.port = int(data.get('port', config.port))
        config.email = data.get('email', config.email)
        
        # 只有提供了密码才更新密码
        if 'password' in data and data['password']:
            config.password = data['password']
        
        config.senders = data.get('senders', config.senders)
        config.subjects = data.get('subjects', config.subjects)
        config.check_interval = int(data.get('check_interval', config.check_interval))
        config.work_start_time = data.get('work_start_time', config.work_start_time)
        config.work_end_time = data.get('work_end_time', config.work_end_time)
        config.enabled = bool(data.get('enabled', config.enabled))
        config.fetch_days = int(data.get('fetch_days', config.fetch_days))
        
        # 如果下载路径改变，尝试创建新目录
        old_path = config.download_path
        new_path = data.get('download_path', old_path)
        if new_path != old_path:
            try:
                os.makedirs(new_path, exist_ok=True)
            except OSError as e:
                return jsonify({
                    'status': 'error',
                    'message': f'无法创建附件保存目录: {str(e)}'
                }), 400
        
        config.download_path = new_path
        config.use_date_folder = bool(data.get('use_date_folder', config.use_date_folder))
        
        db.session.commit()
        
        logger.info(f'邮箱配置 "{config.name}" 更新成功')
        
        # 如果状态发生变化或关键配置变更，刷新调度器
        if config.enabled != old_enabled or config.check_interval != int(data.get('check_interval', config.check_interval)) or \
           config.work_start_time != data.get('work_start_time', config.work_start_time) or \
           config.work_end_time != data.get('work_end_time', config.work_end_time):
            # 导入调度器
            from app import scheduler
            if scheduler:
                scheduler.add_config(config)
                logger.info(f'已更新调度器中的邮箱配置 "{config.name}"')
        
        return jsonify({
            'status': 'success',
            'message': '邮箱配置更新成功',
            'data': config.to_dict()
        })
    except ValueError as e:
        logger.error(f'数据格式错误: {str(e)}')
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'数据格式错误: {str(e)}'
        }), 400
    except Exception as e:
        logger.error(f'更新邮箱配置出错: {str(e)}')
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'更新邮箱配置失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/email_configs/<int:config_id>', methods=['DELETE'])
@login_required
def delete_email_config(config_id):
    """删除邮箱配置"""
    config = EmailConfig.query.get_or_404(config_id)
    
    # 获取邮箱名称用于日志记录
    config_name = config.name
    
    db.session.delete(config)
    db.session.commit()
    
    # 从调度器中移除相关任务
    from app import scheduler
    if scheduler:
        try:
            # 移除与此邮箱配置相关的定时任务
            job_id = f'email_config_{config_id}'
            if scheduler.get_job(job_id):
                scheduler.remove_job(job_id)
                logger.info(f'已从调度器中移除邮箱配置 "{config_name}" 的定时任务')
        except Exception as e:
            logger.warning(f'移除调度器任务时出错: {e}')
    
    logger.info(f'邮箱配置 "{config_name}" 已删除')
    
    return jsonify({
        'status': 'success',
        'message': '邮箱配置删除成功'
    })

@email_attachment_bp.route('/api/email_configs/<int:config_id>/test', methods=['POST'])
@login_required
def test_email_config(config_id):
    """测试邮箱连接"""
    config = EmailConfig.query.get_or_404(config_id)
    
    # 清除之前的错误日志
    error_handler.clear()
    
    # 创建处理器
    from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
    processor = HighPerformanceEmailProcessor(config)

    # 测试连接
    result = processor.connect()
    
    if result:
        processor.disconnect()
        return jsonify({
            'status': 'success',
            'message': '邮箱连接成功'
        })
    else:
        # 获取具体错误信息
        error_message = error_handler.get_last_error() or '邮箱连接失败，请检查配置'
        
        # 针对网易企业邮箱的特殊错误处理
        if 'ERR.LOGIN.REQCODE' in error_message:
            error_message = '网易企业邮箱需要使用授权码登录，请检查：\n1. 是否已在邮箱设置中开启IMAP/SMTP服务\n2. 是否使用了正确的授权码（不是登录密码）\n3. 授权码是否已过期'
        elif '认证失败' in error_message or 'authentication failed' in error_message.lower():
            error_message = '邮箱认证失败，请检查邮箱地址和授权码是否正确'
        elif '连接被拒绝' in error_message or 'connection refused' in error_message.lower():
            error_message = '无法连接到邮箱服务器，请检查IMAP服务器地址和端口是否正确'
        elif '连接超时' in error_message or 'timeout' in error_message.lower():
            error_message = '连接超时，请检查网络连接或稍后重试'
        
        return jsonify({
            'status': 'error',
            'message': error_message
        }), 400

@email_attachment_bp.route('/api/email_configs/test', methods=['POST'])
@login_required
def test_new_email_config():
    """测试新邮箱配置连接（不保存）"""
    data = request.get_json()
    
    # 清除之前的错误日志
    error_handler.clear()
    
    # 创建临时邮箱配置对象，字段名与EmailConfig模型完全匹配
    config = EmailConfig(
        name=data.get('name', '临时配置'),
        server=data.get('server', 'imap.qiye.163.com'),           # 修正：直接使用server
        port=data.get('port', 993),                               # 修正：直接使用port
        email=data.get('email', ''),                              # 修正：直接使用email
        password=data.get('password', ''),
        senders=data.get('senders', ''),                          # 修正：直接使用senders
        subjects=data.get('subjects', ''),                        # 修正：直接使用subjects
        check_interval=data.get('check_interval', 60),
        work_start_time=data.get('work_start_time', '08:00'),
        work_end_time=data.get('work_end_time', '18:00'),
        enabled=data.get('enabled', False),
        download_path=data.get('download_path', 'downloads/email_attachments'),
        use_date_folder=data.get('use_date_folder', True),
        fetch_days=data.get('fetch_days', 10),
        created_by=current_user.username
    )
    
    # 创建处理器
    from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
    processor = HighPerformanceEmailProcessor(config)

    # 测试连接
    result = processor.connect()
    
    if result:
        processor.disconnect()
        return jsonify({
            'status': 'success',
            'message': '邮箱连接成功'
        })
    else:
        # 获取具体错误信息
        error_message = error_handler.get_last_error() or '邮箱连接失败，请检查配置'
        
        # 针对网易企业邮箱的特殊错误处理
        if 'ERR.LOGIN.REQCODE' in error_message:
            error_message = '网易企业邮箱需要使用授权码登录，请检查：\n1. 是否已在邮箱设置中开启IMAP/SMTP服务\n2. 是否使用了正确的授权码（不是登录密码）\n3. 授权码是否已过期'
        elif '认证失败' in error_message or 'authentication failed' in error_message.lower():
            error_message = '邮箱认证失败，请检查邮箱地址和授权码是否正确'
        elif '连接被拒绝' in error_message or 'connection refused' in error_message.lower():
            error_message = '无法连接到邮箱服务器，请检查IMAP服务器地址和端口是否正确'
        elif '连接超时' in error_message or 'timeout' in error_message.lower():
            error_message = '连接超时，请检查网络连接或稍后重试'
        
        return jsonify({
            'status': 'error',
            'message': error_message
        }), 400

@email_attachment_bp.route('/api/email_configs/<int:config_id>/preview', methods=['POST'])
@login_required
def preview_email_attachments(config_id):
    """预览已下载的Excel附件"""
    try:
        # 获取邮箱配置
        config = EmailConfig.query.get_or_404(config_id)
        if not config:
            return jsonify({
                'success': False,
                'error': '邮箱配置不存在'
            }), 404

        # 获取天数参数
        try:
            data = request.get_json() or {}
        except Exception:
            data = {}

        # 如果前端传递了days参数且不为null，使用传递的值；否则使用配置中的抓取天数
        days = data.get('days')
        if days is None:
            days = config.fetch_days or 5  # 默认5天，与配置保持一致

        # 查询已下载的Excel附件（从数据库中获取）
        from datetime import datetime, timedelta
        
        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 查询数据库中的附件记录
        attachments_query = EmailAttachment.query.filter(
            EmailAttachment.email_config_id == config_id,
            EmailAttachment.receive_date >= start_date,
            EmailAttachment.receive_date <= end_date,
            EmailAttachment.filename.like('%.xlsx') | EmailAttachment.filename.like('%.xls'),
            EmailAttachment.file_size > 0  # 确保文件已下载
        ).order_by(EmailAttachment.receive_date.desc())
        
        attachments = attachments_query.all()
        
        # 构建预览结果
        preview_attachments = []
        for attachment in attachments:
            # 检查文件是否实际存在
            file_exists = os.path.exists(attachment.file_path) if attachment.file_path else False
            
            preview_attachments.append({
                "id": attachment.id,
                "filename": attachment.filename,
                "sender": attachment.sender,
                "subject": attachment.subject,
                "receive_date": attachment.receive_date.strftime('%Y-%m-%d %H:%M:%S'),
                "file_size": attachment.file_size,
                "file_path": attachment.file_path,
                "file_exists": file_exists,
                "processed": attachment.processed,
                "process_result": attachment.process_result,
                "status": "已下载" if file_exists else "文件丢失"
            })

        return jsonify({
            'success': True,
            'data': {
                'attachments': preview_attachments,
                'total_emails': len(set([att.sender + att.subject for att in attachments])),  # 估算邮件数量
                'total_attachments': len(preview_attachments),
                'days_searched': days,
                'config_name': config.name,
                'download_path': config.download_path
            },
            'message': f'预览最近{days}天已下载的Excel附件：找到 {len(preview_attachments)} 个附件'
        })
        
    except Exception as e:
        logger.error(f"预览已下载附件时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'预览已下载附件失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/email_configs/<int:config_id>/fetch', methods=['POST'])
@login_required
def fetch_email_attachments(config_id):
    """获取邮件附件"""
    # 获取查询参数
    data = request.get_json() or {}
    
    try:
        # 获取邮箱配置
        config = EmailConfig.query.get_or_404(config_id)
        if not config:
            return jsonify({
                'status': 'error',
                'message': '邮箱配置不存在'
            }), 404
        
        # 获取天数参数，优先使用请求中的值，否则使用配置中的值
        days = data.get('days', config.fetch_days)
            
        # 创建邮件处理器
        from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
        processor = HighPerformanceEmailProcessor(config)
        if not processor.connect():
            return jsonify({
                'status': 'error',
                'message': '连接邮箱失败，请检查配置'
            }), 500
        
        # 获取附件，使用配置中的发件人和主题过滤条件
        result = processor.fetch_attachments(days=days)
        processor.disconnect()
        
        # 构建详细的处理结果
        detailed_result = {
            'status': 'success',
            'message': f'成功获取到 {result["total"]} 封邮件，共处理 {result["downloaded"] + result["skipped"] + result["failed"]} 个附件，新增 {result["new_downloaded"]} 个',
            'data': {
                'total_emails': result['total'],
                'total_attachments': result['downloaded'] + result['skipped'] + result['failed'],
                'downloaded': result['downloaded'],
                'new_downloaded': result['new_downloaded'],  # 添加新增附件数量
                'skipped': result['skipped'],
                'failed': result['failed'],
                'processed_files': result.get('processed_files', []),
                'skipped_files': result.get('skipped_files', []),
                'failed_files': result.get('failed_files', [])
            }
        }
        
        return jsonify(detailed_result)
    except Exception as e:
        logger.error(f"获取邮件附件时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取邮件附件失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/email_attachments', methods=['GET'])
@login_required
def get_email_attachments():
    """获取邮件附件列表"""
    try:
        # 获取查询参数
        config_id = request.args.get('config_id', type=int)
        processed = request.args.get('processed', type=int)  # 0表示未处理，1表示已处理
        result = request.args.get('result')  # 处理结果：success/skipped/error
        start_date = request.args.get('start_date')  # 开始日期：YYYY-MM-DD
        end_date = request.args.get('end_date')  # 结束日期：YYYY-MM-DD
        search = request.args.get('search')  # 搜索关键词
        
        # 构建查询
        query = EmailAttachment.query
        
        if config_id:
            query = query.filter_by(email_config_id=config_id)
        
        if processed is not None:
            query = query.filter_by(processed=(processed == 1))
        
        if result:
            query = query.filter_by(process_result=result)
        
        # 日期范围筛选
        if start_date:
            try:
                start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d').replace(hour=0, minute=0, second=0)
                query = query.filter(EmailAttachment.receive_date >= start_datetime)
            except ValueError:
                return jsonify({
                    'status': 'error',
                    'message': f'无效的开始日期格式: {start_date}，请使用YYYY-MM-DD格式'
                }), 400
        
        if end_date:
            try:
                end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
                query = query.filter(EmailAttachment.receive_date <= end_datetime)
            except ValueError:
                return jsonify({
                    'status': 'error',
                    'message': f'无效的结束日期格式: {end_date}，请使用YYYY-MM-DD格式'
                }), 400
        
        # 关键词搜索
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                db.or_(
                    EmailAttachment.filename.ilike(search_term),
                    EmailAttachment.sender.ilike(search_term),
                    EmailAttachment.subject.ilike(search_term)
                )
            )
        
        # 按接收日期倒序排序
        attachments = query.order_by(EmailAttachment.receive_date.desc()).all()
        
        # 增强附件信息
        result = []
        for attachment in attachments:
            data = attachment.to_dict()
            
            # 添加下载状态
            data['downloaded'] = attachment.file_size > 0
            
            # 添加处理状态描述
            if attachment.processed:
                if attachment.process_result == 'success':
                    data['status_text'] = '处理成功'
                    data['status_class'] = 'text-success'
                    data['status_icon'] = 'fa-check-circle'
                elif attachment.process_result == 'skipped':
                    data['status_text'] = '已跳过'
                    data['status_class'] = 'text-warning'
                    data['status_icon'] = 'fa-exclamation-circle'
                elif attachment.process_result == 'error':
                    data['status_text'] = '处理失败'
                    data['status_class'] = 'text-danger'
                    data['status_icon'] = 'fa-times-circle'
                else:
                    data['status_text'] = '已处理'
                    data['status_class'] = 'text-info'
                    data['status_icon'] = 'fa-info-circle'
            else:
                data['status_text'] = '未处理'
                data['status_class'] = ''
                data['status_icon'] = ''
                
            result.append(data)
        
        return jsonify({
            'status': 'success',
            'data': result,
            'count': len(result)
        })
    except Exception as e:
        logger.error(f"获取邮件附件列表失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取邮件附件列表失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/email_attachments/<int:attachment_id>', methods=['GET'])
@login_required
def get_email_attachment(attachment_id):
    """获取指定邮件附件"""
    attachment = EmailAttachment.query.get_or_404(attachment_id)
    return jsonify({
        'status': 'success',
        'data': attachment.to_dict()
    })

@email_attachment_bp.route('/api/email_attachments/<int:attachment_id>/process', methods=['POST'])
@login_required
def process_attachment(attachment_id):
    """处理邮件附件"""
    attachment = EmailAttachment.query.get_or_404(attachment_id)
    
    # 获取参数
    mapping_id = request.json.get('mapping_id')
    
    if not mapping_id:
        return jsonify({
            'status': 'error',
            'message': '未提供Excel映射配置ID'
        }), 400
    
    # 获取映射配置
    mapping = ExcelMapping.query.get_or_404(mapping_id)
    
    # 检查文件是否存在
    if not os.path.exists(attachment.file_path):
        return jsonify({
            'status': 'error',
            'message': f'文件不存在: {attachment.file_path}'
        }), 404
    
    # 创建处理器
    processor = EnhancedExcelParser()

    try:
        # 处理文件
        result = processor.parse_order_file(attachment.file_path)
        
        # 更新附件状态
        attachment.processed = True
        attachment.process_date = datetime.datetime.utcnow()
        attachment.process_result = 'success'
        attachment.process_message = result['message']
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'文件处理成功: {result["message"]}',
            'data': result.get('data')
        })
    
    except Exception as e:
        # 更新附件状态
        attachment.processed = True
        attachment.process_date = datetime.datetime.utcnow()
        attachment.process_result = 'error'
        attachment.process_message = str(e)
        db.session.commit()
        
        return jsonify({
            'status': 'error',
            'message': f'文件处理失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/order_data', methods=['GET'])
@login_required
def get_order_data():
    """获取订单数据列表"""
    orders = OrderData.query.order_by(OrderData.created_at.desc()).all()
    return jsonify({
        'status': 'success',
        'data': [order.to_dict() for order in orders]
    })

@email_attachment_bp.route('/api/order_data/stats', methods=['GET'])
@login_required
def get_order_data_stats():
    """获取订单数据统计信息"""
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import func
        
        # 总订单数
        total = OrderData.query.count()
        
        # 按分类统计
        engineering = OrderData.query.filter(
            OrderData.classification == 'engineering'
        ).count()
        
        production = OrderData.query.filter(
            OrderData.classification == 'production'
        ).count()
        
        # 今日新增订单数
        today = datetime.now().date()
        tomorrow = today + timedelta(days=1)
        today_count = OrderData.query.filter(
            OrderData.created_at >= today,
            OrderData.created_at < tomorrow
        ).count()
        
        return jsonify({
            'status': 'success',
            'data': {
                'total': total,
                'engineering': engineering,
                'production': production,
                'today': today_count
            }
        })
    except Exception as e:
        logger.error(f'获取订单数据统计失败: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': f'获取订单数据统计失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/order_data/<int:order_id>', methods=['GET'])
@login_required
def get_order(order_id):
    """获取指定订单数据"""
    order = OrderData.query.get_or_404(order_id)
    return jsonify({
        'status': 'success',
        'data': order.to_dict()
    })

@email_attachment_bp.route('/api/order_data/<int:order_id>', methods=['DELETE'])
@login_required
def delete_order_data(order_id):
    """删除指定订单数据"""
    order = OrderData.query.get_or_404(order_id)
    
    try:
        db.session.delete(order)
        db.session.commit()
        return jsonify({
            'status': 'success',
            'message': '订单数据删除成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'删除订单数据失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/order_data/export')
@login_required
def export_order_data():
    """导出订单数据为Excel文件"""
    try:
        # 获取查询参数
        order_type = request.args.get('type')  # engineering 或 production
        
        # 构建查询
        query = OrderData.query
        
        if order_type:
            query = query.filter(OrderData.classification == order_type)
        
        orders = query.all()
        
        # 准备导出数据
        data = [order.to_dict() for order in orders]
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            sheet_name = '订单数据'
            if order_type == 'engineering':
                sheet_name = '工程订单数据'
            elif order_type == 'production':
                sheet_name = '量产订单数据'
            
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        output.seek(0)
        
        # 生成文件名
        filename_prefix = '订单数据'
        if order_type == 'engineering':
            filename_prefix = '工程订单数据'
        elif order_type == 'production':
            filename_prefix = '量产订单数据'
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'{filename_prefix}_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'导出订单数据失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/email_attachments/<int:attachment_id>/download', methods=['GET'])
@login_required
def download_attachment(attachment_id):
    """下载邮件附件"""
    attachment = EmailAttachment.query.get_or_404(attachment_id)
    
    # 检查文件是否存在
    if attachment.file_size > 0 and os.path.exists(attachment.file_path):
        # 文件已下载，直接发送
        return send_file(
            attachment.file_path,
            as_attachment=True,
            download_name=attachment.filename
        )
    
    # 对于跳过的附件，尝试重新下载
    if attachment.process_result == 'skipped':
        try:
            # 获取邮箱配置
            config = EmailConfig.query.get(attachment.email_config_id)
            if not config:
                return jsonify({
                    'status': 'error',
                    'message': '找不到邮箱配置信息'
                }), 404
            
            # 连接到邮箱
            config = EmailConfig.query.get(attachment.email_config_id)
            if not config:
                return jsonify({
                    'status': 'error',
                    'message': '邮箱配置不存在'
                }), 404

            from app.utils.high_performance_email_processor import HighPerformanceEmailProcessor
            processor = HighPerformanceEmailProcessor(config)
            if not processor.connect():
                return jsonify({
                    'status': 'error',
                    'message': '连接邮箱失败'
                }), 500
            
            # 由于高性能处理器不支持单个附件下载，直接返回错误
            processor.disconnect()
            return jsonify({
                'status': 'error',
                'message': '高性能邮件处理器不支持单个附件下载功能'
            }), 501
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'下载附件失败: {str(e)}'
            }), 500
    
    # 文件不存在且不是跳过的附件
    return jsonify({
        'status': 'error',
        'message': f'文件不存在或无法下载: {attachment.file_path}'
    }), 404


@email_attachment_bp.route('/api/email_attachments/batch_process', methods=['POST'])
@login_required
def batch_process_attachments():
    """批量处理所有未处理的邮件附件Excel文件并汇总"""
    try:
        data = request.get_json()
        mapping_id = data.get('mapping_id')
        
        if not mapping_id:
            return jsonify({
                'status': 'error',
                'message': '缺少必要参数: mapping_id'
            }), 400
        
        # 获取映射配置
        mapping = ExcelMapping.query.get_or_404(mapping_id)
        
        # 获取所有未处理的附件
        attachments = EmailAttachment.query.filter_by(processed=False).all()
        
        if not attachments:
            return jsonify({
                'status': 'success',
                'message': '没有需要处理的附件',
                'data': {
                    'total_files': 0,
                    'success_files': 0,
                    'error_files': 0,
                    'total_records': 0
                }
            })
        
        # 收集所有Excel文件路径
        file_paths = []
        for attachment in attachments:
            if os.path.exists(attachment.file_path) and attachment.file_size > 0:
                # 检查是否是Excel文件
                if attachment.filename.lower().endswith(('.xlsx', '.xls')):
                    file_paths.append(attachment.file_path)
        
        if not file_paths:
            return jsonify({
                'status': 'success',
                'message': '没有找到可处理的Excel文件',
                'data': {
                    'total_files': 0,
                    'success_files': 0,
                    'error_files': 0,
                    'total_records': 0
                }
            })
        
        # 创建处理器
        processor = EnhancedExcelParser()

        # 批量处理文件
        processed_count = 0
        failed_count = 0

        # 逐个处理文件
        for file_path in file_paths:
            try:
                result = processor.parse_order_file(file_path)
                if result.get('status') == 'success':
                    processed_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                failed_count += 1
                logger.error(f"处理文件失败 {file_path}: {e}")

        # 更新附件处理状态
        for file_path in file_paths:
            attachment = EmailAttachment.query.filter_by(file_path=file_path).first()
            if attachment:
                attachment.processed = True
                attachment.process_date = datetime.datetime.now()

                # 简化状态更新
                if processed_count > failed_count:
                    attachment.process_result = 'success'
                    attachment.process_message = '批量处理成功'
                else:
                    attachment.process_result = 'error'
                    attachment.process_message = '处理失败'

        db.session.commit()

        # 构建返回结果
        response_data = {
            'status': 'success',
            'message': f'批量处理完成，成功: {processed_count}，失败: {failed_count}',
            'data': {
                'total_files': len(file_paths),
                'success_files': processed_count,
                'error_files': failed_count,
                'total_records': processed_count
            }
        }
        
        return jsonify(response_data)
    except Exception as e:
        logger.error(f"批量处理附件时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'批量处理附件时出错: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/order_data/download_summary', methods=['GET'])
@login_required
def download_summary_file():
    """下载汇总文件"""
    try:
        # 获取文件路径
        file_path = request.args.get('path')
        if not file_path:
            return jsonify({
                'status': 'error',
                'message': '未提供文件路径'
            }), 400
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({
                'status': 'error',
                'message': f'文件不存在: {file_path}'
            }), 404
        
        # 获取文件名
        filename = os.path.basename(file_path)
        
        # 使用send_file发送文件
        return send_file(
            file_path,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        logger.error(f"下载汇总文件时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'下载汇总文件时出错: {str(e)}'
        }), 500

# Excel映射配置相关API
@email_attachment_bp.route('/api/excel_mappings', methods=['GET'])
@login_required
def get_excel_mappings():
    """获取所有Excel映射配置"""
    try:
        mappings = ExcelMapping.query.all()
        return jsonify({
            'status': 'success',
            'data': [mapping.to_dict() for mapping in mappings]
        })
    except Exception as e:
        logger.error(f"获取Excel映射配置出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取Excel映射配置失败: {str(e)}'
        }), 500

@login_required
def create_excel_mapping():
    """创建Excel映射配置"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'field_mappings']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'status': 'error',
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查名称是否已存在
        if ExcelMapping.query.filter_by(name=data['name']).first():
            return jsonify({
                'status': 'error',
                'message': f'配置名称 "{data["name"]}" 已存在，请使用其他名称'
            }), 400
        
        # 处理字段映射数据，确保是JSON格式
        field_mappings = {}
        for mapping in data['field_mappings']:
            excel_field = mapping.get('excel_field')
            system_field = mapping.get('system_field')
            if excel_field and system_field:
                field_mappings[excel_field] = system_field
        
        # 创建映射配置
        mapping = ExcelMapping(
            name=data.get('name'),
            sheet_name=data.get('sheet_name'),
            start_row=int(data.get('start_row', 2)),
            header_row=int(data.get('header_row', 1)),
            field_mappings=json.dumps(field_mappings),
            key_fields=data.get('key_fields', ''),
            date_format=data.get('date_format', '%Y-%m-%d'),
            created_by=current_user.username
        )
        
        db.session.add(mapping)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Excel映射配置创建成功',
            'data': mapping.to_dict()
        })
    except Exception as e:
        logger.error(f"创建Excel映射配置出错: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'创建Excel映射配置失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/excel_mappings/<int:mapping_id>', methods=['GET'])
@login_required
def get_excel_mapping(mapping_id):
    """获取指定Excel映射配置"""
    mapping = ExcelMapping.query.get_or_404(mapping_id)
    return jsonify({
        'status': 'success',
        'data': mapping.to_dict()
    })

@email_attachment_bp.route('/api/excel_mappings/<int:mapping_id>', methods=['PUT'])
@login_required
def update_excel_mapping(mapping_id):
    """更新Excel映射配置"""
    try:
        mapping = ExcelMapping.query.get_or_404(mapping_id)
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'field_mappings']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'status': 'error',
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查名称是否与其他配置冲突
        name_conflict = ExcelMapping.query.filter(
            ExcelMapping.name == data.get('name'),
            ExcelMapping.id != mapping_id
        ).first()
        if name_conflict:
            return jsonify({
                'status': 'error',
                'message': f'配置名称 "{data["name"]}" 已被使用，请使用其他名称'
            }), 400
        
        # 处理字段映射数据，确保是JSON格式
        field_mappings = {}
        for mapping_item in data['field_mappings']:
            excel_field = mapping_item.get('excel_field')
            system_field = mapping_item.get('system_field')
            if excel_field and system_field:
                field_mappings[excel_field] = system_field
        
        # 更新字段
        mapping.name = data.get('name')
        mapping.sheet_name = data.get('sheet_name')
        mapping.start_row = int(data.get('start_row', mapping.start_row))
        mapping.header_row = int(data.get('header_row', mapping.header_row))
        mapping.field_mappings = json.dumps(field_mappings)
        mapping.key_fields = data.get('key_fields', mapping.key_fields)
        mapping.date_format = data.get('date_format', mapping.date_format)
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Excel映射配置更新成功',
            'data': mapping.to_dict()
        })
    except Exception as e:
        logger.error(f"更新Excel映射配置出错: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'更新Excel映射配置失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/excel_mappings/<int:mapping_id>', methods=['DELETE'])
@login_required
def delete_excel_mapping(mapping_id):
    """删除Excel映射配置"""
    try:
        mapping = ExcelMapping.query.get_or_404(mapping_id)
        db.session.delete(mapping)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Excel映射配置删除成功'
        })
    except Exception as e:
        logger.error(f"删除Excel映射配置出错: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'删除Excel映射配置失败: {str(e)}'
        }), 500

@email_attachment_bp.route('/api/email_attachments/dashboard', methods=['GET'])
@login_required
def get_dashboard_data():
    """获取邮件附件仪表盘数据"""
    try:
        # 获取统计数据
        total_attachments = EmailAttachment.query.count()
        processed_attachments = EmailAttachment.query.filter_by(processed=True).count()
        
        # 获取今日新增附件数
        today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_attachments = EmailAttachment.query.filter(EmailAttachment.created_at >= today).count()
        
        # 获取生成的订单数量
        total_orders = OrderData.query.count()
        
        # 获取处理状态统计
        success_count = EmailAttachment.query.filter_by(processed=True, process_result='success').count()
        skipped_count = EmailAttachment.query.filter_by(processed=True, process_result='skipped').count()
        error_count = EmailAttachment.query.filter_by(processed=True).filter(
            ~EmailAttachment.process_result.in_(['success', 'skipped'])
        ).count()
        pending_count = EmailAttachment.query.filter_by(processed=False).count()
        
        # 获取近7天的趋势数据
        dates = []
        received_counts = []
        processed_counts = []
        
        for i in range(6, -1, -1):
            day = datetime.datetime.now() - datetime.timedelta(days=i)
            day_start = day.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day.replace(hour=23, minute=59, second=59, microsecond=999999)
            
            # 当天日期字符串
            date_str = f"{day.month}/{day.day}"
            dates.append(date_str)
            
            # 当天接收的附件数
            received_count = EmailAttachment.query.filter(
                EmailAttachment.receive_date >= day_start,
                EmailAttachment.receive_date <= day_end
            ).count()
            received_counts.append(received_count)
            
            # 当天处理的附件数
            processed_count = EmailAttachment.query.filter(
                EmailAttachment.processed == True,
                EmailAttachment.process_date >= day_start,
                EmailAttachment.process_date <= day_end
            ).count()
            processed_counts.append(processed_count)
        
        # 获取最近处理的附件
        recent_attachments = EmailAttachment.query.filter_by(processed=True).order_by(
            EmailAttachment.process_date.desc()
        ).limit(5).all()
        
        recent_data = []
        for attachment in recent_attachments:
            recent_data.append({
                'id': attachment.id,
                'filename': attachment.filename,
                'sender': attachment.sender,
                'process_date': attachment.process_date.isoformat() if attachment.process_date else None,
                'process_result': attachment.process_result,
                'subject': attachment.subject
            })
        
        # 构建返回数据
        response_data = {
            'status': 'success',
            'data': {
                'statistics': {
                    'total': total_attachments,
                    'processed': processed_attachments,
                    'orders': total_orders,
                    'today': today_attachments,
                    'processing_rate': round(processed_attachments / total_attachments * 100, 1) if total_attachments > 0 else 0
                },
                'charts': {
                    'status': {
                        'success': success_count,
                        'skipped': skipped_count,
                        'failed': error_count,
                        'pending': pending_count
                    },
                    'trend': {
                        'dates': dates,
                        'received': received_counts,
                        'processed': processed_counts
                    }
                },
                'recent_processed': recent_data
            }
        }
        
        # 记录API调用和响应
        logger.info(f"仪表盘API调用成功，返回数据: {response_data}")
        
        return jsonify(response_data)
    except Exception as e:
        logger.error(f"获取看板数据失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取看板数据失败: {str(e)}'
        }), 500
