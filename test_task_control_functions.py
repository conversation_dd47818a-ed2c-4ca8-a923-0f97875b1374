#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务控制面板功能测试脚本
测试任务控制面板的所有按钮和功能
"""

import requests
import json
import time
from datetime import datetime

class TaskControlFunctionTest:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.logged_in = False
        self.current_task_id = None
        
    def log_test(self, test_name, status, message="", details=None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⏭️"
        print(f"{status_icon} {test_name}: {message}")
        
    def login(self, username="admin", password="admin"):
        """登录系统"""
        try:
            login_page = self.session.get(f"{self.base_url}/auth/login")
            if login_page.status_code != 200:
                return False
                
            login_data = {'username': username, 'password': password}
            response = self.session.post(f"{self.base_url}/auth/login", 
                                       data=login_data, allow_redirects=True)
            
            test_response = self.session.get(f"{self.base_url}/orders/semi-auto")
            if test_response.status_code == 200 and "手动导入订单" in test_response.text:
                self.logged_in = True
                return True
            return False
        except:
            return False
    
    def test_start_auto_processing(self):
        """测试自动处理按钮"""
        print("\n🔍 测试自动处理功能...")
        
        start_data = {
            "mode": "auto",
            "email_configs": [],
            "parse_settings": {
                "mode": "auto",
                "skip_duplicates": True,
                "backup_original": True
            }
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/processing/start",
                                       json=start_data,
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   自动处理响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        self.current_task_id = data.get('task_id')
                        self.log_test("自动处理启动", "PASS", 
                                    f"成功启动自动处理任务，ID: {self.current_task_id}")
                        return True
                    else:
                        self.log_test("自动处理启动", "FAIL", 
                                    f"启动失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("自动处理启动", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("自动处理启动", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("自动处理启动", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_start_step_processing(self):
        """测试分步处理按钮"""
        print("\n🔍 测试分步处理功能...")
        
        start_data = {
            "mode": "step",
            "email_configs": [],
            "parse_settings": {
                "mode": "manual",
                "skip_duplicates": True,
                "backup_original": True
            }
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/processing/start",
                                       json=start_data,
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   分步处理响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        task_id = data.get('task_id')
                        self.log_test("分步处理启动", "PASS", 
                                    f"成功启动分步处理任务，ID: {task_id}")
                        return task_id
                    else:
                        self.log_test("分步处理启动", "FAIL", 
                                    f"启动失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("分步处理启动", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("分步处理启动", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("分步处理启动", "FAIL", f"请求异常: {str(e)}")
        
        return None
    
    def test_pause_processing(self):
        """测试暂停处理按钮"""
        print("\n🔍 测试暂停处理功能...")
        
        if not self.current_task_id:
            self.log_test("暂停处理", "SKIP", "没有可用的任务ID")
            return False
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/processing/{self.current_task_id}/pause",
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   暂停响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        self.log_test("暂停处理", "PASS", 
                                    f"成功暂停任务: {data.get('message', '无消息')}")
                        return True
                    else:
                        self.log_test("暂停处理", "FAIL", 
                                    f"暂停失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("暂停处理", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("暂停处理", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("暂停处理", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_resume_processing(self):
        """测试恢复处理按钮"""
        print("\n🔍 测试恢复处理功能...")
        
        if not self.current_task_id:
            self.log_test("恢复处理", "SKIP", "没有可用的任务ID")
            return False
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/processing/{self.current_task_id}/resume",
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   恢复响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        self.log_test("恢复处理", "PASS", 
                                    f"成功恢复任务: {data.get('message', '无消息')}")
                        return True
                    else:
                        self.log_test("恢复处理", "FAIL", 
                                    f"恢复失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("恢复处理", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("恢复处理", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("恢复处理", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_stop_processing(self):
        """测试停止处理按钮"""
        print("\n🔍 测试停止处理功能...")
        
        if not self.current_task_id:
            self.log_test("停止处理", "SKIP", "没有可用的任务ID")
            return False
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/processing/{self.current_task_id}/stop",
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   停止响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        self.log_test("停止处理", "PASS", 
                                    f"成功停止任务: {data.get('message', '无消息')}")
                        return True
                    else:
                        self.log_test("停止处理", "FAIL", 
                                    f"停止失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("停止处理", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("停止处理", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("停止处理", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_scheduled_processing(self):
        """测试定时执行功能"""
        print("\n🔍 测试定时执行功能...")
        
        schedule_data = {
            "schedule_time": "2025-06-25T10:00:00",
            "mode": "auto",
            "email_configs": [],
            "parse_settings": {
                "mode": "auto",
                "skip_duplicates": True,
                "backup_original": True
            }
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/processing/schedule",
                                       json=schedule_data,
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   定时执行响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        self.log_test("定时执行设置", "PASS", 
                                    f"成功设置定时任务: {data.get('message', '无消息')}")
                        return True
                    else:
                        self.log_test("定时执行设置", "FAIL", 
                                    f"设置失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("定时执行设置", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("定时执行设置", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("定时执行设置", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_task_status_monitoring(self):
        """测试任务状态监控"""
        print("\n🔍 测试任务状态监控...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/v2/orders/processing/tasks")
            
            print(f"   任务列表响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        tasks = data.get('data', [])
                        self.log_test("任务状态监控", "PASS", 
                                    f"成功获取任务列表，共 {len(tasks)} 个任务")
                        return tasks
                    else:
                        self.log_test("任务状态监控", "FAIL", 
                                    f"获取失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("任务状态监控", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("任务状态监控", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("任务状态监控", "FAIL", f"请求异常: {str(e)}")
        
        return []
    
    def run_all_tests(self):
        """运行所有任务控制功能测试"""
        print("🚀 开始任务控制面板功能测试...")
        print("=" * 60)
        
        # 登录
        if not self.login():
            print("❌ 无法登录系统")
            return
        
        # 测试自动处理
        self.test_start_auto_processing()
        
        # 等待一下让任务启动
        time.sleep(1)
        
        # 测试暂停处理
        self.test_pause_processing()
        
        # 测试恢复处理
        self.test_resume_processing()
        
        # 测试停止处理
        self.test_stop_processing()
        
        # 测试分步处理
        self.test_start_step_processing()
        
        # 测试定时执行
        self.test_scheduled_processing()
        
        # 测试任务状态监控
        self.test_task_status_monitoring()
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 任务控制面板功能测试结果")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        skipped_tests = len([r for r in self.test_results if r['status'] == 'SKIP'])
        
        print(f"总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"⏭️ 跳过: {skipped_tests}")
        
        if total_tests > 0:
            print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  - {result['test_name']}: {result['message']}")

if __name__ == "__main__":
    test_suite = TaskControlFunctionTest()
    test_suite.run_all_tests()
