[{"test_name": "页面访问", "status": "PASS", "message": "手动导入订单页面可正常访问", "details": null, "timestamp": "2025-06-24T21:06:03.097934"}, {"test_name": "页面内容", "status": "FAIL", "message": "页面标题未正确显示", "details": null, "timestamp": "2025-06-24T21:06:03.097934"}, {"test_name": "关键元素-workflow-steps", "status": "FAIL", "message": "元素未找到", "details": null, "timestamp": "2025-06-24T21:06:03.097934"}, {"test_name": "关键元素-step1Panel", "status": "FAIL", "message": "元素未找到", "details": null, "timestamp": "2025-06-24T21:06:03.097934"}, {"test_name": "关键元素-step2Panel", "status": "FAIL", "message": "元素未找到", "details": null, "timestamp": "2025-06-24T21:06:03.097934"}, {"test_name": "关键元素-step3Panel", "status": "FAIL", "message": "元素未找到", "details": null, "timestamp": "2025-06-24T21:06:03.097934"}, {"test_name": "关键元素-startBtn", "status": "FAIL", "message": "元素未找到", "details": null, "timestamp": "2025-06-24T21:06:03.097934"}, {"test_name": "关键元素-pauseBtn", "status": "FAIL", "message": "元素未找到", "details": null, "timestamp": "2025-06-24T21:06:03.098935"}, {"test_name": "关键元素-stopBtn", "status": "FAIL", "message": "元素未找到", "details": null, "timestamp": "2025-06-24T21:06:03.098935"}, {"test_name": "API端点-邮箱配置API", "status": "PASS", "message": "端点可访问 (状态码: 200)", "details": null, "timestamp": "2025-06-24T21:06:03.104936"}, {"test_name": "API端点-本地附件扫描API", "status": "PASS", "message": "端点可访问 (状态码: 200)", "details": null, "timestamp": "2025-06-24T21:06:03.131935"}, {"test_name": "API端点-处理任务启动API", "status": "PASS", "message": "端点可访问 (状态码: 200)", "details": null, "timestamp": "2025-06-24T21:06:03.135934"}, {"test_name": "API端点-FT订单数据API", "status": "FAIL", "message": "异常状态码: 404", "details": null, "timestamp": "2025-06-24T21:06:03.139934"}, {"test_name": "API端点-CP订单数据API", "status": "FAIL", "message": "异常状态码: 404", "details": null, "timestamp": "2025-06-24T21:06:03.145934"}, {"test_name": "JS函数-startProcessing", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.151934"}, {"test_name": "JS函数-pauseProcessing", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.151934"}, {"test_name": "JS函数-stopProcessing", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "JS函数-switchStep", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "JS函数-scanAttachments", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "JS函数-processSelectedAttachments", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "JS函数-exportSelectedOrderData", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "JS函数-refreshOrderData", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "JS函数-testEmailConnection", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "JS函数-previewAttachments", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "JS函数-showAddEmailModal", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "JS函数-refreshEmailConfigs", "status": "FAIL", "message": "函数未找到", "details": null, "timestamp": "2025-06-24T21:06:03.152934"}, {"test_name": "UI按钮-自动处理按钮", "status": "FAIL", "message": "按钮元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.160756"}, {"test_name": "UI按钮-分步处理按钮", "status": "FAIL", "message": "按钮元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.160756"}, {"test_name": "UI按钮-暂停按钮", "status": "FAIL", "message": "按钮元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.160756"}, {"test_name": "UI按钮-停止按钮", "status": "FAIL", "message": "按钮元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.160756"}, {"test_name": "UI按钮-扫描附件按钮", "status": "FAIL", "message": "按钮元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.160756"}, {"test_name": "UI按钮-处理选中按钮", "status": "FAIL", "message": "按钮元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.160756"}, {"test_name": "UI按钮-批量处理按钮", "status": "FAIL", "message": "按钮元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.160756"}, {"test_name": "UI表单-邮箱配置表单", "status": "FAIL", "message": "表单元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.161756"}, {"test_name": "UI表单-邮箱配置选择", "status": "FAIL", "message": "表单元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.161756"}, {"test_name": "UI表单-订单类型筛选", "status": "FAIL", "message": "表单元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.161756"}, {"test_name": "UI表单-日期范围筛选", "status": "FAIL", "message": "表单元素缺失", "details": null, "timestamp": "2025-06-24T21:06:03.161756"}]