#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
附件获取与解析功能测试脚本
测试步骤2中所有附件处理相关的功能
"""

import requests
import json
import time
from datetime import datetime

class AttachmentFunctionTest:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.logged_in = False
        
    def log_test(self, test_name, status, message="", details=None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⏭️"
        print(f"{status_icon} {test_name}: {message}")
        
    def login(self, username="admin", password="admin"):
        """登录系统"""
        try:
            login_page = self.session.get(f"{self.base_url}/auth/login")
            if login_page.status_code != 200:
                return False
                
            login_data = {'username': username, 'password': password}
            response = self.session.post(f"{self.base_url}/auth/login", 
                                       data=login_data, allow_redirects=True)
            
            test_response = self.session.get(f"{self.base_url}/orders/semi-auto")
            if test_response.status_code == 200 and "手动导入订单" in test_response.text:
                self.logged_in = True
                return True
            return False
        except:
            return False
    
    def test_scan_local_attachments(self):
        """测试扫描本地附件功能"""
        print("\n🔍 测试扫描本地附件...")
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/attachments/scan/local",
                                       json={},
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   扫描响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        attachments = data.get('data', [])
                        self.log_test("扫描本地附件", "PASS", 
                                    f"成功扫描到 {len(attachments)} 个附件")
                        return attachments
                    else:
                        self.log_test("扫描本地附件", "FAIL", 
                                    f"扫描失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("扫描本地附件", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("扫描本地附件", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("扫描本地附件", "FAIL", f"请求异常: {str(e)}")
        
        return []
    
    def test_scan_email_attachments(self):
        """测试扫描邮箱附件功能"""
        print("\n🔍 测试扫描邮箱附件...")
        
        scan_data = {
            "email_config_id": None,  # 使用默认配置
            "days": 7
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/attachments/scan",
                                       json=scan_data,
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   邮箱扫描响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        attachments = data.get('data', [])
                        self.log_test("扫描邮箱附件", "PASS", 
                                    f"成功扫描到 {len(attachments)} 个邮箱附件")
                        return attachments
                    else:
                        self.log_test("扫描邮箱附件", "FAIL", 
                                    f"扫描失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("扫描邮箱附件", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("扫描邮箱附件", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("扫描邮箱附件", "FAIL", f"请求异常: {str(e)}")
        
        return []
    
    def test_process_attachments(self, attachment_ids=None):
        """测试处理附件功能"""
        print("\n🔍 测试处理附件...")
        
        if not attachment_ids:
            attachment_ids = []
        
        process_data = {
            "attachment_ids": attachment_ids,
            "parse_mode": "auto",
            "skip_duplicates": True,
            "backup_original": True
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/attachments/process",
                                       json=process_data,
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   处理响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        self.log_test("处理附件", "PASS", 
                                    f"附件处理请求成功: {data.get('message', '无消息')}")
                        return True
                    else:
                        self.log_test("处理附件", "FAIL", 
                                    f"处理失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("处理附件", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("处理附件", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("处理附件", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_processing_task_control(self):
        """测试处理任务控制功能"""
        print("\n🔍 测试处理任务控制...")
        
        # 测试启动处理任务
        start_data = {
            "mode": "auto",
            "email_configs": [],
            "parse_settings": {
                "mode": "auto",
                "skip_duplicates": True,
                "backup_original": True
            }
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/processing/start",
                                       json=start_data,
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   启动任务响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        task_id = data.get('task_id')
                        self.log_test("启动处理任务", "PASS", 
                                    f"成功启动处理任务，ID: {task_id}")
                        return task_id
                    else:
                        self.log_test("启动处理任务", "FAIL", 
                                    f"启动失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("启动处理任务", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("启动处理任务", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("启动处理任务", "FAIL", f"请求异常: {str(e)}")
        
        return None
    
    def test_task_status(self, task_id):
        """测试任务状态查询"""
        print(f"\n🔍 测试任务状态查询 (ID: {task_id})...")
        
        if not task_id:
            self.log_test("查询任务状态", "SKIP", "没有可用的任务ID")
            return
        
        try:
            response = self.session.get(f"{self.base_url}/api/v2/orders/processing/{task_id}/status")

            print(f"   状态查询响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        status = data.get('data', {}).get('status', 'unknown')
                        self.log_test("查询任务状态", "PASS", 
                                    f"成功查询任务状态: {status}")
                    else:
                        self.log_test("查询任务状态", "FAIL", 
                                    f"查询失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("查询任务状态", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("查询任务状态", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("查询任务状态", "FAIL", f"请求异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有附件处理功能测试"""
        print("🚀 开始附件获取与解析功能测试...")
        print("=" * 60)
        
        # 登录
        if not self.login():
            print("❌ 无法登录系统")
            return
        
        # 扫描本地附件
        local_attachments = self.test_scan_local_attachments()
        
        # 扫描邮箱附件
        email_attachments = self.test_scan_email_attachments()
        
        # 处理附件
        attachment_ids = []
        if local_attachments:
            attachment_ids = [att.get('id') for att in local_attachments[:2] if att.get('id')]
        
        self.test_process_attachments(attachment_ids)
        
        # 测试任务控制
        task_id = self.test_processing_task_control()
        
        # 查询任务状态
        self.test_task_status(task_id)
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 附件处理功能测试结果")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        skipped_tests = len([r for r in self.test_results if r['status'] == 'SKIP'])
        
        print(f"总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"⏭️ 跳过: {skipped_tests}")
        
        if total_tests > 0:
            print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  - {result['test_name']}: {result['message']}")

if __name__ == "__main__":
    test_suite = AttachmentFunctionTest()
    test_suite.run_all_tests()
