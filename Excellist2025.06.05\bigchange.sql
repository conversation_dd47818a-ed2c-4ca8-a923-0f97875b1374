select * from(select distinct a.HANDLER_ID, a.TB_PN, a.HB_PN, a.KIT_PN, a.device, a.STAGE from(SELECT  id, HANDLER_ID
, EQP_CLASS/*, HANDLER_TYPE, TESTER_ID, SOCKET_PN, EQP_CLASS, EQP_TYPE, TEMPERATURE_RANGE
*/
, TEMPERATURE_CAPACITY
/*, LOT_ID, STATUS
*/
, HB_PN, IFNULL(TB_PN,'NA') AS TB_PN, KIT_PN, STAGE, DEVICE

, TESTER_CONFIG, HANDLER_CONFIG
/*, EVENT_TIME, created_at, updated_at*/
 FROM aps.eqp_status)a left join
 (
 select  /*id, PROD_ID, COMPANY_ID, */
 
 STAGE, DEVICE, HANDLER_CONFIG
 /*, CHIP_ID, PKG_PN, RECIPE_FILE_NAME, RECIPE_FILE_PATH, APPROVAL_STATE, FAC_ID, EDIT_STATE, EDIT_TIME, EDIT_USER, EVENT, EVENT_KEY, EVENT_TIME, EVENT_USER, EVENT_MSG, CREATE_TIME, CREATE_USER, PROD_TYPE, EQP_TYPE, MODEL_ID, SIMP_RECIPE_FILE_PATH, RECIPE_VER, SUB_FAC
 */
 , KIT_PN, SOCKET_PN
 /*, FAMILY, COORDINATE_ONE, COORDINATE_TWO, COORDINATE_THREE, created_at, updated_at
 */
 from  aps.et_recipe_file 
 )b on a.HANDLER_CONFIG=b.HANDLER_CONFIG
   left join(
 select /* id, TEST_SPEC_ID, TEST_SPEC_NAME, TEST_SPEC_VER, INV_ID, TEST_SPEC_TYPE, APPROVAL_STATE, ACTV_YN
 , PROD_ID, CHIP_ID, PKG_PN, COMPANY_ID, DISABLE_USER, DISABLE_REASON, DISABLE_TIME, NOTE, APPROVE_USER, APPROVE_TIME, ORT_QTY, REMAIN_QTY, STANDARD_YIELD, LOW_YIELD, DOWN_YIELD, TEST_AREA, FT_PROGRAM, QA_PROGRAM, GU_PROGRAM, TIB, TEST_TIME, UPH, SUFFIX_CODE, TESTER_CONFIG, GU_COMPARE_PARAM, STA_COMPARE_PARAM, DNR, SITE, DPAT, BS_NAME, GU_NAME, C_SPEC, TEST_ENG, TEST_OPERATION, ORDER_COMMENT, HIGH_YIELD, VISION_LOSS_YIELD, VISION_YIELD, LOSS_YIELD, RETEST_YN, FT_PROGRAM_PATH, QA_PROGRAM_PATH, GU_PROGRAM_PATH, EFFECTIVE_TIME, TPL_RULE_TEMP, TPL_RULE_TEMP_PATH, ALARM_DATE, FAC_ID, EDIT_STATE, EDIT_TIME, EDIT_USER, EVENT, EVENT_KEY, EVENT_TIME, EVENT_USER, EVENT_MSG, CREATE_TIME, CREATE_USER, created_at, updated_at
  ,*/
  upper(STAGE) as STAGE, TESTER, DEVICE, IFNULL(TB_PN,'NA') as NEW_TB_PN, HB_PN AS NEW_HB_PN, HANDLER, TEMPERATURE
 
 from aps.et_ft_test_spec)c on a.EQP_CLASS=c.HANDLER
 where b.device=c.device and b.STAGE=c.STAGE
 )aa left join(SELECT LOT_ID, DEVICE, upper(STAGE) as STAGE FROM aps.ET_WAIT_LOT)bb on aa.DEVICE=bb.DEVICE and aa.STAGE=substr(bb.STAGE,1,4)
 where bb.LOT_ID is not null