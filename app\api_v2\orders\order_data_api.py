"""
订单数据管理API
支持FT订单和CP订单的现代化数据管理功能
"""

from flask import Blueprint, request, jsonify, send_file
from flask_login import login_required
import logging
from datetime import datetime, timedelta
import os
import tempfile
import pandas as pd
from app.api.routes import get_db_connection  # 修正导入路径

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

order_data_bp = Blueprint('order_data', __name__)

def success_response(data, pagination=None, statistics=None):
    """成功响应格式"""
    response = {
        'success': True,
        'data': data
    }
    if pagination:
        response['pagination'] = pagination
    if statistics:
        response['statistics'] = statistics
    return jsonify(response)

def error_response(message):
    """错误响应格式"""
    return jsonify({
        'success': False,
        'message': message
    }), 400

@order_data_bp.route('/data/ft_summary', methods=['GET'])
@login_required
def get_ft_summary():
    """获取FT订单汇总数据"""
    try:
        page = int(request.args.get('page', 1))
        page_size = request.args.get('page_size', '50')
        
        # 处理显示全部的情况
        if page_size == 'all':
            page_size = 10000
        else:
            page_size = int(page_size)
        
        # 筛选参数
        field = request.args.get('field', '')
        operator = request.args.get('operator', 'contains')
        value = request.args.get('value', '')
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 构建基础查询
            base_query = "SELECT * FROM ft_order_summary"
            where_conditions = []
            params = []
            
            # 应用筛选条件
            if field and value:
                if operator == 'contains':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'%{value}%')
                elif operator == 'equals':
                    where_conditions.append(f"{field} = %s")
                    params.append(value)
                elif operator == 'starts_with':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'{value}%')
                elif operator == 'ends_with':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'%{value}')
            
            # 构建完整查询
            if where_conditions:
                base_query += " WHERE " + " AND ".join(where_conditions)
            
            # 计算总数
            count_query = f"SELECT COUNT(*) as count FROM ({base_query}) as filtered"
            cursor.execute(count_query, params)
            count_result = cursor.fetchone()
            total_count = count_result[0] if count_result else 0
            
            # 添加排序和分页
            base_query += " ORDER BY id DESC"
            offset = (page - 1) * page_size
            paginated_query = f"{base_query} LIMIT %s OFFSET %s"
            params.extend([page_size, offset])
            
            # 执行分页查询
            cursor.execute(paginated_query, params)
            results = cursor.fetchall()
            
            # 转换为字典格式
            data = []
            for row in results:
                if isinstance(row, dict):
                    # 如果已经是字典，直接处理
                    row_dict = {}
                    for column_name, value in row.items():
                        if isinstance(value, datetime):
                            row_dict[column_name] = value.strftime('%Y-%m-%d %H:%M:%S')
                        elif hasattr(value, 'strftime'):  # 处理日期类型
                            row_dict[column_name] = value.strftime('%Y-%m-%d')
                        else:
                            row_dict[column_name] = value
                    data.append(row_dict)
                else:
                    # 如果是元组，需要获取列名
                    data.append(dict(row))
            
            # 计算统计信息
            cursor.execute("SELECT COUNT(*) as count FROM ft_order_summary")
            total_result = cursor.fetchone()
            total_records = total_result[0] if total_result else 0

            cursor.execute("SELECT COUNT(*) as count FROM ft_order_summary WHERE classification_result LIKE '%工程%'")
            eng_result = cursor.fetchone()
            engineering_count = eng_result[0] if eng_result else 0

            cursor.execute("SELECT COUNT(*) as count FROM ft_order_summary WHERE classification_result LIKE '%量产%'")
            prod_result = cursor.fetchone()
            production_count = prod_result[0] if prod_result else 0
            
            # 分页信息
            total_pages = (total_count + page_size - 1) // page_size
            pagination = {
                'current_page': page,
                'total_pages': total_pages,
                'total': total_count,
                'start': offset + 1 if total_count > 0 else 0,
                'end': min(offset + page_size, total_count)
            }
            
            # 统计信息
            statistics = {
                'total': total_records,
                'valid': total_records,  # 假设所有记录都是有效的
                'engineering': engineering_count,
                'production': production_count
            }
            
            return success_response(data, pagination, statistics)
            
        finally:
            cursor.close()
            conn.close()
            
    except Exception as e:
        logger.error(f"获取FT订单数据失败: {str(e)}")
        return error_response(f"获取FT订单数据失败: {str(e)}")

@order_data_bp.route('/data/cp_summary', methods=['GET'])
@login_required
def get_cp_summary():
    """获取CP订单汇总数据"""
    try:
        page = int(request.args.get('page', 1))
        page_size = request.args.get('page_size', '50')
        
        # 处理显示全部的情况
        if page_size == 'all':
            page_size = 10000
        else:
            page_size = int(page_size)
        
        # 筛选参数
        field = request.args.get('field', '')
        operator = request.args.get('operator', 'contains')
        value = request.args.get('value', '')
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 构建基础查询
            base_query = "SELECT * FROM cp_order_summary"
            where_conditions = []
            params = []
            
            # 应用筛选条件
            if field and value:
                if operator == 'contains':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'%{value}%')
                elif operator == 'equals':
                    where_conditions.append(f"{field} = %s")
                    params.append(value)
                elif operator == 'starts_with':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'{value}%')
                elif operator == 'ends_with':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'%{value}')
            
            # 构建完整查询
            if where_conditions:
                base_query += " WHERE " + " AND ".join(where_conditions)
            
            # 计算总数
            count_query = f"SELECT COUNT(*) as count FROM ({base_query}) as filtered"
            cursor.execute(count_query, params)
            count_result = cursor.fetchone()
            total_count = count_result[0] if count_result else 0
            
            # 添加排序和分页
            base_query += " ORDER BY id DESC"
            offset = (page - 1) * page_size
            paginated_query = f"{base_query} LIMIT %s OFFSET %s"
            params.extend([page_size, offset])
            
            # 执行分页查询
            cursor.execute(paginated_query, params)
            results = cursor.fetchall()
            
            # 转换为字典格式
            data = []
            for row in results:
                if isinstance(row, dict):
                    # 如果已经是字典，直接处理
                    row_dict = {}
                    for column_name, value in row.items():
                        if isinstance(value, datetime):
                            row_dict[column_name] = value.strftime('%Y-%m-%d %H:%M:%S')
                        elif hasattr(value, 'strftime'):  # 处理日期类型
                            row_dict[column_name] = value.strftime('%Y-%m-%d')
                        else:
                            row_dict[column_name] = value
                    data.append(row_dict)
                else:
                    # 如果是元组，需要获取列名
                    data.append(dict(row))
            
            # 计算统计信息
            cursor.execute("SELECT COUNT(*) as count FROM cp_order_summary")
            total_result = cursor.fetchone()
            total_records = total_result[0] if total_result else 0
            
            # CP订单可能没有工程/量产分类，使用其他统计
            engineering_count = 0
            production_count = total_records
            
            # 分页信息
            total_pages = (total_count + page_size - 1) // page_size
            pagination = {
                'current_page': page,
                'total_pages': total_pages,
                'total': total_count,
                'start': offset + 1 if total_count > 0 else 0,
                'end': min(offset + page_size, total_count)
            }
            
            # 统计信息
            statistics = {
                'total': total_records,
                'valid': total_records,
                'engineering': engineering_count,
                'production': production_count
            }
            
            return success_response(data, pagination, statistics)
            
        finally:
            cursor.close()
            conn.close()
            
    except Exception as e:
        logger.error(f"获取CP订单数据失败: {str(e)}")
        return error_response(f"获取CP订单数据失败: {str(e)}")

@order_data_bp.route('/data/export', methods=['POST'])
@login_required
def export_order_data():
    """导出订单数据"""
    try:
        # 支持POST请求的JSON数据
        data = request.get_json() or {}
        order_type = data.get('order_type', 'FT')
        export_format = data.get('format', 'excel')
        selected_ids = data.get('selected_ids', [])
        include_fields = data.get('include_fields', [])
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 根据订单类型选择表
            table_name = 'ft_order_summary' if order_type.upper() == 'FT' else 'cp_order_summary'

            # 构建查询
            if selected_ids:
                placeholders = ','.join(['%s'] * len(selected_ids))
                query = f"SELECT * FROM {table_name} WHERE id IN ({placeholders})"
                cursor.execute(query, selected_ids)
            else:
                cursor.execute(f"SELECT * FROM {table_name}")

            results = cursor.fetchall()

            # 如果没有数据，返回空文件
            if not results:
                return jsonify({
                    'success': False,
                    'message': '没有找到要导出的数据'
                }), 404

            # 创建DataFrame (假设results是字典列表)
            df = pd.DataFrame(results)
            
            # 创建临时文件
            temp_dir = tempfile.mkdtemp()
            filename = f'{order_type.lower()}_orders_{datetime.now().strftime("%Y%m%d_%H%M%S")}'

            if export_format == 'csv':
                file_path = os.path.join(temp_dir, f'{filename}.csv')
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                mimetype = 'text/csv'
            else:
                file_path = os.path.join(temp_dir, f'{filename}.xlsx')
                df.to_excel(file_path, index=False)
                mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

            return send_file(
                file_path,
                as_attachment=True,
                download_name=f'{filename}.{export_format}',
                mimetype=mimetype
            )
            
        finally:
            cursor.close()
            conn.close()
            
    except Exception as e:
        logger.error(f"导出FT订单数据失败: {str(e)}")
        return error_response(f"导出失败: {str(e)}")

@order_data_bp.route('/data/refresh', methods=['POST'])
@login_required
def refresh_order_data():
    """刷新订单数据"""
    try:
        # 这里可以添加刷新数据的逻辑，比如重新扫描文件、更新缓存等
        # 目前返回成功响应
        return jsonify({
            'success': True,
            'message': '订单数据刷新成功',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"刷新订单数据失败: {str(e)}")
        return error_response(f"刷新失败: {str(e)}")

@order_data_bp.route('/statistics', methods=['GET'])
@login_required
def get_order_statistics():
    """获取订单统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # 获取FT订单统计
            cursor.execute("SELECT COUNT(*) as count FROM ft_order_summary")
            ft_result = cursor.fetchone()
            ft_total = ft_result[0] if ft_result else 0

            # 获取CP订单统计
            cursor.execute("SELECT COUNT(*) as count FROM cp_order_summary")
            cp_result = cursor.fetchone()
            cp_total = cp_result[0] if cp_result else 0

            # 获取工程订单统计
            cursor.execute("SELECT COUNT(*) as count FROM ft_order_summary WHERE classification_result LIKE '%工程%'")
            engineering_result = cursor.fetchone()
            engineering_total = engineering_result[0] if engineering_result else 0

            # 获取量产订单统计
            cursor.execute("SELECT COUNT(*) as count FROM ft_order_summary WHERE classification_result LIKE '%量产%'")
            production_result = cursor.fetchone()
            production_total = production_result[0] if production_result else 0

            statistics = {
                'total': ft_total + cp_total,
                'ft_orders': ft_total,
                'cp_orders': cp_total,
                'engineering_orders': engineering_total,
                'production_orders': production_total,
                'last_updated': datetime.now().isoformat()
            }

            return jsonify({
                'success': True,
                'data': statistics
            })

        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        logger.error(f"获取订单统计失败: {str(e)}")
        return error_response(f"获取统计信息失败: {str(e)}")
