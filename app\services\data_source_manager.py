"""
数据源管理器 - 支持MySQL和Excel数据源的智能切换
当MySQL数据有问题时，自动切换到Excel数据源
"""

import logging
import os
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy import text
from app import db
from app.utils.db_helper import get_mysql_connection

logger = logging.getLogger(__name__)

# 在类的开头添加唯一字段映射配置
UNIQUE_FIELD_MAPPING = {
    'eqp_status': {
        'primary_key': 'id',  # 主键
        'business_key': 'HANDLER_ID',  # 业务主键
        'display_key': 'EQP_ID',  # 显示主键
        'datetime_fields': ['created_at', 'updated_at', 'UPDATE_TIME']  # 日期时间字段
    },
    'et_ft_test_spec': {
        'primary_key': 'id',
        'business_key': 'TEST_SPEC_ID',
        'display_key': 'TEST_SPEC_ID',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'et_uph_eqp': {
        'primary_key': 'id',
        'business_key': 'DEVICE',  # 组合业务键需要额外处理
        'display_key': 'DEVICE',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'ET_UPH_EQP': {  # 大写版本映射
        'primary_key': 'id',
        'business_key': 'DEVICE',
        'display_key': 'DEVICE',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'ct': {
        'primary_key': 'id',
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'CT': {  # 大写版本映射
        'primary_key': 'id',
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'TCC_INV': {
        'primary_key': 'id',
        'business_key': 'id',  # 没有明显业务键，使用ID
        'display_key': 'id',
        'datetime_fields': ['created_at', 'updated_at']
    },
    'et_wait_lot': {
        'primary_key': 'LOT_ID',  # 使用LOT_ID作为主键
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at', 'CREATE_TIME']
    },
    'lotprioritydone': {
        'primary_key': 'id',
        'business_key': 'LOT_ID',
        'display_key': 'LOT_ID',
        'datetime_fields': ['created_at', 'updated_at', 'timestamp']
    },
    'devicepriorityconfig': {
        'primary_key': 'id',
        'business_key': 'device',
        'display_key': 'device',
        'datetime_fields': ['created_at', 'updated_at', 'from_time', 'end_time', 'refresh_time']
    },
    'lotpriorityconfig': {
        'primary_key': 'id',
        'business_key': 'device',
        'display_key': 'device',
        'datetime_fields': ['created_at', 'updated_at', 'refresh_time']
    }
}

class DataSourceManager:
    """数据源管理器 - 智能切换MySQL和Excel数据源"""
    

    # 缓存机制优化
    _cache = {}
    _cache_timeout = 300  # 5分钟缓存
    
    def _normalize_datetime_field(self, value):
        """智能日期时间字段标准化"""
        if value is None or value == '' or value == 'None':
            return None
            
        # 如果已经是datetime对象，直接格式化
        if hasattr(value, 'strftime'):
            return value.strftime('%Y-%m-%d %H:%M:%S')
            
        # 转换字符串格式
        try:
            from datetime import datetime
            import re
            
            # 处理GMT格式: 'Tue, 17 Jun 2025 17:17:22 GMT'
            if isinstance(value, str) and 'GMT' in value:
                # 移除星期和GMT，提取核心日期时间
                clean_value = re.sub(r'^[A-Za-z]{3},\s*', '', value)  # 移除 "Tue, "
                clean_value = re.sub(r'\s*GMT\s*$', '', clean_value)   # 移除 " GMT"
                # 转换月份名称为数字
                month_map = {
                    'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
                    'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
                    'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
                }
                for month_name, month_num in month_map.items():
                    clean_value = clean_value.replace(month_name, month_num)
                
                # 重新排列为标准格式 "17 06 2025 17:17:22" -> "2025-06-17 17:17:22"
                parts = clean_value.split()
                if len(parts) >= 4:
                    day, month, year, time = parts[0], parts[1], parts[2], parts[3]
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)} {time}"
            
            # 处理ISO格式或其他标准格式
            if isinstance(value, str):
                # 特殊处理：只有时间部分的情况（如 '08:00:00'）
                if re.match(r'^\d{1,2}:\d{2}:\d{2}$', value):
                    # 将时间部分与当前日期结合
                    current_date = datetime.now().strftime('%Y-%m-%d')
                    return f"{current_date} {value}"
                
                # 尝试多种日期格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d %H:%M:%S.%f',
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%dT%H:%M:%S.%f',
                    '%Y-%m-%dT%H:%M:%SZ',
                    '%Y-%m-%d',
                    '%m/%d/%Y',
                    '%m/%d/%Y %H:%M:%S',
                    '%H:%M:%S'  # 添加纯时间格式支持
                ]
                
                for fmt in formats:
                    try:
                        dt = datetime.strptime(value, fmt)
                        # 如果是纯时间格式，与当前日期结合
                        if fmt == '%H:%M:%S':
                            current_date = datetime.now().strftime('%Y-%m-%d')
                            return f"{current_date} {value}"
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue
                        
                # 如果所有格式都失败，返回当前时间
                logger.warning(f"无法解析日期格式: {value}，使用当前时间")
                return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
        except Exception as e:
            logger.warning(f"日期转换失败: {value}, 错误: {e}")
            from datetime import datetime
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
        return str(value)
    
    def _preprocess_record_data(self, table_name: str, data: Dict) -> Dict:
        """记录数据预处理 - 统一处理日期字段和数据类型"""
        processed_data = data.copy()
        
        # 获取表配置
        field_config = UNIQUE_FIELD_MAPPING.get(table_name, {})
        datetime_fields = field_config.get('datetime_fields', [])
        
        # 处理日期时间字段
        for field in datetime_fields:
            if field in processed_data:
                processed_data[field] = self._normalize_datetime_field(processed_data[field])
        
        # 处理优先级字段的数据类型转换
        if 'priority' in processed_data:
            priority_value = processed_data['priority']
            if isinstance(priority_value, str):
                # 字符串优先级映射为数字
                priority_mapping = {
                    'high': 1,
                    'medium': 2,
                    'normal': 2,
                    'low': 3,
                    'urgent': 0,
                    'critical': 0
                }
                processed_data['priority'] = priority_mapping.get(priority_value.lower(), 2)  # 默认medium
            elif isinstance(priority_value, (int, float)):
                processed_data['priority'] = int(priority_value)
        
        # 特殊处理：自动添加created_at和updated_at
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if 'created_at' not in processed_data or not processed_data.get('created_at'):
            processed_data['created_at'] = current_time
            
        if 'updated_at' not in processed_data or not processed_data.get('updated_at'):
            processed_data['updated_at'] = current_time
        
        # 移除空值字段（避免数据库约束错误）
        filtered_data = {}
        for key, value in processed_data.items():
            if value is not None and value != '':
                filtered_data[key] = value
                
        return filtered_data
    
    def _apply_filters(self, data: List[Dict], filters: List[Dict]) -> List[Dict]:
        """应用筛选条件到数据集"""
        if not filters or not data:
            return data
        
        filtered_data = []
        
        for record in data:
            match_all_filters = True
            
            for filter_condition in filters:
                field = filter_condition.get('field', '')
                operator = filter_condition.get('operator', 'contains')
                value = filter_condition.get('value', '')
                
                if not field:
                    continue
                
                # 获取记录中的字段值
                record_value = record.get(field, '')
                
                # 转换为字符串进行比较（处理None值）
                if record_value is None:
                    record_value = ''
                else:
                    record_value = str(record_value).lower()
                
                filter_value = str(value).lower()
                
                # 应用不同的操作符
                field_matches = False
                try:
                    if operator == 'contains':
                        field_matches = filter_value in record_value
                    elif operator == 'equals':
                        field_matches = record_value == filter_value
                    elif operator == 'starts_with':
                        field_matches = record_value.startswith(filter_value)
                    elif operator == 'ends_with':
                        field_matches = record_value.endswith(filter_value)
                    elif operator == 'not_equals':
                        field_matches = record_value != filter_value
                    elif operator == 'is_empty':
                        field_matches = record_value == '' or record_value is None
                    elif operator == 'is_not_empty':
                        field_matches = record_value != '' and record_value is not None
                    elif operator == 'greater_than':
                        # 尝试数值比较
                        try:
                            field_matches = float(record_value) > float(filter_value)
                        except (ValueError, TypeError):
                            field_matches = record_value > filter_value
                    elif operator == 'less_than':
                        # 尝试数值比较
                        try:
                            field_matches = float(record_value) < float(filter_value)
                        except (ValueError, TypeError):
                            field_matches = record_value < filter_value
                    else:
                        # 默认使用包含操作
                        field_matches = filter_value in record_value
                        
                except Exception as e:
                    logger.warning(f"筛选条件应用失败: {field} {operator} {value}, 错误: {e}")
                    field_matches = False
                
                # 如果任何一个筛选条件不匹配，则该记录不符合要求
                if not field_matches:
                    match_all_filters = False
                    break
            
            # 只有所有筛选条件都匹配才保留该记录
            if match_all_filters:
                filtered_data.append(record)
        
        logger.info(f"筛选条件: {filters}")
        logger.info(f"筛选结果: 从 {len(data)} 条记录筛选到 {len(filtered_data)} 条记录")
        
        return filtered_data
    
    def _get_cached_data(self, cache_key, fetch_func):
        """通用缓存获取方法"""
        import time
        current_time = time.time()
        
        if cache_key in self._cache:
            data, timestamp = self._cache[cache_key]
            if current_time - timestamp < self._cache_timeout:
                return data
        
        # 缓存过期或不存在，重新获取
        data = fetch_func()
        self._cache[cache_key] = (data, current_time)
        return data
    
    def clear_cache(self):
        """清理缓存"""
        self._cache.clear()
    def __init__(self):
        self.current_source = 'mysql'  # 默认使用MySQL
        self.excel_path = r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\Excellist2025.06.05"
        self._mysql_available = None  # 延迟检查，避免应用上下文问题
        self._excel_available = None  # 延迟检查
    
    def _get_mysql_table_data(self, table_name: str, limit: int = None) -> Dict[str, Dict]:
        """通用MySQL表数据获取方法 - 处理任何ID范围和唯一字段"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取表的唯一字段配置
                field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                    'primary_key': 'id',
                    'business_key': 'id', 
                    'display_key': 'id'
                })
                
                # 查询所有数据，按ID排序确保一致性
                if limit and limit > 0:
                    cursor.execute(f"""
                        SELECT * FROM {table_name} 
                        WHERE id IS NOT NULL
                        ORDER BY id
                        LIMIT {limit}
                    """)
                else:
                    cursor.execute(f"""
                        SELECT * FROM {table_name} 
                        WHERE id IS NOT NULL
                        ORDER BY id
                    """)
                
                table_data = {}
                for row in cursor.fetchall():
                    # 使用ID作为主键确保唯一性（无论ID从何开始）
                    primary_key = str(row.get(field_config['primary_key'], ''))
                    
                    # 保留所有原始数据
                    table_data[primary_key] = dict(row)
                    
                    # 根据表类型添加标准化字段
                    if table_name == 'eqp_status':
                        table_data[primary_key].update({
                            'EQP_ID': str(row.get('DEVICE', '') or row.get('HANDLER_ID', '') or primary_key),
                            'EQP_NAME': str(row.get('HANDLER_TYPE', '') or row.get('DEVICE', '') or '未知设备'),
                            'EQP_STATUS': str(row.get('STATUS', 'UNKNOWN')),
                            'TESTER_ID': str(row.get('TESTER_ID', '')),
                            'HANDLER_ID': str(row.get('HANDLER_ID', '')),
                            'FAC_ID': str(row.get('FAC_ID', '')),
                            'UPDATE_TIME': row.get('updated_at', row.get('UPDATE_TIME')),
                            'available': str(row.get('STATUS', '')).upper() in ['RUN', 'IDLE', 'SETUP', 'READY', 'ACTIVE', 'ONLINE']
                        })
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(table_data)} 条{table_name}数据")
            return table_data
            
        except Exception as e:
            logger.error(f"MySQL获取{table_name}数据失败: {e}")
            return {}
        
    @property
    def mysql_available(self):
        """延迟检查MySQL可用性"""
        if self._mysql_available is None:
            self._check_mysql_availability()
        return self._mysql_available
    
    @property
    def excel_available(self):
        """延迟检查Excel可用性"""
        if self._excel_available is None:
            self._check_excel_availability()
        return self._excel_available
    
    def _check_mysql_availability(self):
        """检查MySQL可用性"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',  # 使用aps数据库
                charset='utf8mb4'
            )
            connection.close()
            self._mysql_available = True
            logger.info("✅ MySQL数据源可用")
        except Exception as e:
            self._mysql_available = False
            logger.warning(f"❌ MySQL数据源不可用: {e}")
    
    def _check_excel_availability(self):
        """检查Excel文件可用性"""
        try:
            if os.path.exists(self.excel_path):
                required_files = [
                    'ET_WAIT_LOT.xlsx',
                    'ET_FT_TEST_SPEC.xlsx', 
                    'ET_RECIPE_FILE.xlsx',
                    'EQP_STATUS.xlsx',
                    'ET_UPH_EQP.xlsx',
                    'devicepriorityconfig.xlsx',
                    'lotpriorityconfig.xlsx'
                ]
                
                missing_files = []
                for file in required_files:
                    if not os.path.exists(os.path.join(self.excel_path, file)):
                        missing_files.append(file)
                
                if missing_files:
                    self._excel_available = False
                    logger.warning(f"❌ Excel数据源不完整，缺少文件: {missing_files}")
                else:
                    self._excel_available = True
                    logger.info("✅ Excel数据源可用")
            else:
                self._excel_available = False
                logger.warning(f"❌ Excel数据源路径不存在: {self.excel_path}")
        except Exception as e:
            self._excel_available = False
            logger.warning(f"❌ Excel数据源检查失败: {e}")
    
    def _check_data_sources(self):
        """检查数据源可用性 - 保留原方法名以兼容"""
        self._check_mysql_availability()
        self._check_excel_availability()
    
    def get_wait_lot_data(self) -> Tuple[List[Dict], str]:
        """获取待排产批次数据 - 智能数据源切换"""
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_wait_lot_from_mysql()
                if data:
                    logger.info(f"📊 从MySQL获取到 {len(data)} 条待排产批次数据")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取待排产数据失败: {e}")
                self._mysql_available = False
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_wait_lot_from_excel()
                if data:
                    logger.info(f"📊 从Excel获取到 {len(data)} 条待排产批次数据")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取待排产数据失败: {e}")
        
        logger.error("❌ 所有数据源都不可用")
        return [], "None"
    
    def get_test_spec_data(self) -> Tuple[Dict[str, Dict], str]:
        """获取测试规范数据 - 智能数据源切换"""
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_test_spec_from_mysql()
                if data:
                    logger.info(f"🔬 从MySQL获取到 {len(data)} 条测试规范数据")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取测试规范数据失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_test_spec_from_excel()
                if data:
                    logger.info(f"🔬 从Excel获取到 {len(data)} 条测试规范数据")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取测试规范数据失败: {e}")
        
        return {}, "None"
    
    def get_recipe_file_data(self) -> Tuple[Dict[str, Dict], str]:
        """获取工艺配方数据 - 智能数据源切换"""
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_recipe_file_from_mysql()
                if data:
                    logger.info(f"📄 从MySQL获取到 {len(data)} 条工艺配方数据")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取工艺配方数据失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_recipe_file_from_excel()
                if data:
                    logger.info(f"📄 从Excel获取到 {len(data)} 条工艺配方数据")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取工艺配方数据失败: {e}")
        
        return {}, "None"
    
    def get_equipment_status_data(self) -> Tuple[Dict[str, Dict], str]:
        """获取设备状态数据 - 智能数据源切换"""
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_equipment_status_from_mysql()
                if data:
                    logger.info(f"🏭 从MySQL获取到 {len(data)} 条设备状态数据")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取设备状态数据失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_equipment_status_from_excel()
                if data:
                    logger.info(f"🏭 从Excel获取到 {len(data)} 条设备状态数据")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取设备状态数据失败: {e}")
        
        return {}, "None"
    
    def get_uph_data(self) -> Tuple[Dict[str, Dict], str]:
        """获取UPH数据 - 智能数据源切换"""
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_uph_from_mysql()
                if data:
                    logger.info(f"⚡ 从MySQL获取到 {len(data)} 条UPH数据")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取UPH数据失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_uph_from_excel()
                if data:
                    logger.info(f"⚡ 从Excel获取到 {len(data)} 条UPH数据")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取UPH数据失败: {e}")
        
        return {}, "None"
    
    def get_priority_configs(self) -> Tuple[Dict[str, Dict], str]:
        """获取优先级配置数据 - 智能数据源切换"""
        # 优先尝试MySQL
        if self.mysql_available:
            try:
                data = self._get_priority_configs_from_mysql()
                device_count = len(data.get('device', {}))
                lot_count = len(data.get('lot', {}))
                if device_count > 0 or lot_count > 0:
                    logger.info(f"🎯 从MySQL获取到优先级配置 - 设备: {device_count}, 批次: {lot_count}")
                    return data, "MySQL"
            except Exception as e:
                logger.warning(f"MySQL获取优先级配置失败: {e}")
        
        # 备用Excel数据源
        if self.excel_available:
            try:
                data = self._get_priority_configs_from_excel()
                device_count = len(data.get('device', {}))
                lot_count = len(data.get('lot', {}))
                if device_count > 0 or lot_count > 0:
                    logger.info(f"🎯 从Excel获取到优先级配置 - 设备: {device_count}, 批次: {lot_count}")
                    return data, "Excel"
            except Exception as e:
                logger.error(f"Excel获取优先级配置失败: {e}")
        
        return {'device': {}, 'lot': {}}, "None"
    
    # MySQL数据获取方法
    def _get_wait_lot_from_mysql(self) -> List[Dict]:
        """从MySQL获取待排产批次数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID, CREATE_TIME,
                           FAC_ID, FLOW_ID, FLOW_VER, WIP_STATE, PROC_STATE, HOLD_STATE
                    FROM ET_WAIT_LOT
                    WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL AND LOT_ID != ''
                    ORDER BY CREATE_TIME DESC
                """)
                
                wait_lots = []
                for row in cursor.fetchall():
                    wait_lots.append({
                        'LOT_ID': row[0] or '',
                        'DEVICE': row[1] or '',
                        'STAGE': row[2] or '',
                        'GOOD_QTY': row[3] or 0,
                        'PKG_PN': row[4] or '',
                        'CHIP_ID': row[5] or '',
                        'CREATE_TIME': row[6],
                        'FAC_ID': row[7] or '',
                        'FLOW_ID': row[8] or '',
                        'FLOW_VER': row[9] or '',
                        'WIP_STATE': row[10] or '',
                        'PROC_STATE': row[11] or '',
                        'HOLD_STATE': row[12] or ''
                    })
            
            connection.close()
            return wait_lots
            
        except Exception as e:
            logger.error(f"MySQL获取待排产批次数据失败: {e}")
            return []
    
    def _get_test_spec_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取测试规范数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 直接查询所有字段，不指定特定字段名
                cursor.execute("SELECT * FROM et_ft_test_spec WHERE id IS NOT NULL ORDER BY id")
                
                test_specs = {}
                for i, row in enumerate(cursor.fetchall()):
                    # 使用组合键或序号作为主键
                    device = str(row.get('DEVICE', '') or f'DEVICE_{i:03d}')
                    stage = str(row.get('STAGE', ''))
                    
                    key = f"{device}|{stage}" if stage else device
                    
                    # 直接使用数据库返回的所有字段
                    test_specs[key] = dict(row)
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(test_specs)} 条测试规范数据")
            return test_specs
            
        except Exception as e:
            logger.error(f"MySQL获取测试规范数据失败: {e}")
            return {}
    
    def _get_recipe_file_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取工艺配方数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT DEVICE, STAGE, PKG_PN, CHIP_ID, RECIPE_FILE, RECIPE_VERSION, TESTER_TYPE, HANDLER_TYPE
                    FROM et_recipe_file
                    WHERE DEVICE IS NOT NULL AND DEVICE != ''
                """)
                
                recipe_files = {}
                for row in cursor.fetchall():
                    device, stage, pkg_pn, chip_id = row[0] or '', row[1] or '', row[2] or '', row[3] or ''
                    
                    keys = [
                        f"{device}|{stage}|{pkg_pn}|{chip_id}",
                        f"{device}|{stage}|{pkg_pn}",
                        f"{device}|{stage}",
                        f"{device}"
                    ]
                    
                    recipe_data = {
                        'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'CHIP_ID': chip_id,
                        'RECIPE_FILE': row[4] or '', 'RECIPE_VERSION': row[5] or '',
                        'TESTER_TYPE': row[6] or '', 'HANDLER_TYPE': row[7] or ''
                    }
                    
                    for key in keys:
                        if key not in recipe_files:
                            recipe_files[key] = recipe_data
            
            connection.close()
            return recipe_files
            
        except Exception as e:
            logger.error(f"MySQL获取工艺配方数据失败: {e}")
            return {}
    
    def _get_equipment_status_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取设备状态数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 查询所有数据，按ID排序确保一致性
                cursor.execute("""
                    SELECT * FROM eqp_status 
                    WHERE id IS NOT NULL
                    ORDER BY id
                """)
                
                equipment_status = {}
                for row in cursor.fetchall():
                    # 使用ID作为主键确保唯一性（无论ID从何开始）
                    unique_key = str(row['id'])
                    
                    # 保留所有原始数据
                    equipment_status[unique_key] = dict(row)
                    
                    # 添加标准化字段供前端使用
                    equipment_status[unique_key].update({
                        'EQP_ID': str(row.get('DEVICE', '') or row.get('HANDLER_ID', '') or unique_key),
                        'EQP_NAME': str(row.get('HANDLER_TYPE', '') or row.get('DEVICE', '') or '未知设备'),
                        'EQP_STATUS': str(row.get('STATUS', 'UNKNOWN')),
                        'TESTER_ID': str(row.get('TESTER_ID', '')),
                        'HANDLER_ID': str(row.get('HANDLER_ID', '')),
                        'FAC_ID': str(row.get('FAC_ID', '')),
                        'UPDATE_TIME': row.get('updated_at', row.get('UPDATE_TIME')),
                        'available': str(row.get('STATUS', '')).upper() in ['RUN', 'IDLE', 'SETUP', 'READY', 'ACTIVE', 'ONLINE']
                    })
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(equipment_status)} 条设备状态数据")
            return equipment_status
            
        except Exception as e:
            logger.error(f"MySQL获取设备状态数据失败: {e}")
            return {}
    
    def _get_uph_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取UPH数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 直接查询所有字段
                cursor.execute("SELECT * FROM et_uph_eqp WHERE id IS NOT NULL ORDER BY id")
                
                uph_data = {}
                for i, row in enumerate(cursor.fetchall()):
                    # 使用组合键
                    device = str(row.get('DEVICE', '') or f'DEVICE_{i:03d}')
                    stage = str(row.get('STAGE', ''))
                    pkg_pn = str(row.get('PKG_PN', ''))
                    
                    keys = [
                        f"{device}|{stage}|{pkg_pn}" if pkg_pn else f"{device}|{stage}",
                        f"{device}|{stage}",
                        device
                    ]
                    
                    # 直接使用数据库返回的所有字段
                    uph_info = dict(row)
                    for key in keys:
                        if key not in uph_data:
                            uph_data[key] = uph_info
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(uph_data)} 条UPH数据")
            return uph_data
            
        except Exception as e:
            logger.error(f"MySQL获取UPH数据失败: {e}")
            return {}
    
    def _get_priority_configs_from_mysql(self) -> Dict[str, Dict]:
        """从MySQL获取优先级配置数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps_system',  # 优先级配置在aps_system数据库
                charset='utf8mb4'
            )
            
            device_priority = {}
            lot_priority = {}
            
            with connection.cursor() as cursor:
                # 获取设备优先级配置
                try:
                    cursor.execute("SELECT * FROM device_priority_config")
                    for row in cursor.fetchall():
                        if len(row) >= 2:  # 确保有足够的字段
                            device, stage = row[0] or '', row[1] or ''
                            keys = [f"{device}|{stage}", f"{device}"]
                            for key in keys:
                                if key not in device_priority:
                                    device_priority[key] = {
                                        'DEVICE': device,
                                        'STAGE': stage,
                                        'PRIORITY': row[2] if len(row) > 2 else 1
                                    }
                except Exception as e:
                    logger.warning(f"获取设备优先级配置失败: {e}")
                
                # 获取批次优先级配置
                try:
                    cursor.execute("SELECT * FROM lot_priority_config")
                    for row in cursor.fetchall():
                        if len(row) >= 1:  # 确保有足够的字段
                            lot_id = row[0] or ''
                            if lot_id:
                                lot_priority[lot_id] = {
                                    'LOT_ID': lot_id,
                                    'PRIORITY': row[1] if len(row) > 1 else 1
                                }
                except Exception as e:
                    logger.warning(f"获取批次优先级配置失败: {e}")
            
            connection.close()
            return {'device': device_priority, 'lot': lot_priority}
            
        except Exception as e:
            logger.error(f"MySQL获取优先级配置失败: {e}")
            return {'device': {}, 'lot': {}}
    
    # Excel数据获取方法
    def _get_wait_lot_from_excel(self) -> List[Dict]:
        """从Excel获取待排产批次数据"""
        file_path = os.path.join(self.excel_path, 'ET_WAIT_LOT.xlsx')
        df = pd.read_excel(file_path)
        
        wait_lots = []
        for _, row in df.iterrows():
            if pd.notna(row.get('GOOD_QTY', 0)) and row.get('GOOD_QTY', 0) > 0:
                wait_lots.append({
                    'LOT_ID': str(row.get('LOT_ID', '')),
                    'DEVICE': str(row.get('DEVICE', '')),
                    'STAGE': str(row.get('STAGE', '')),
                    'GOOD_QTY': int(row.get('GOOD_QTY', 0)),
                    'PKG_PN': str(row.get('PKG_PN', '')),
                    'CHIP_ID': str(row.get('CHIP_ID', '')),
                    'CREATE_TIME': row.get('CREATE_TIME'),
                    'FAC_ID': str(row.get('FAC_ID', '')),
                    'FLOW_ID': str(row.get('FLOW_ID', '')),
                    'FLOW_VER': str(row.get('FLOW_VER', '')),
                    'WIP_STATE': str(row.get('WIP_STATE', '')),
                    'PROC_STATE': str(row.get('PROC_STATE', '')),
                    'HOLD_STATE': str(row.get('HOLD_STATE', ''))
                })
        
        return wait_lots
    
    def _get_test_spec_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取测试规范数据"""
        file_path = os.path.join(self.excel_path, 'ET_FT_TEST_SPEC.xlsx')
        df = pd.read_excel(file_path)
        
        test_specs = {}
        for _, row in df.iterrows():
            device, stage = str(row.get('DEVICE', '')), str(row.get('STAGE', ''))
            pkg_pn, chip_id = str(row.get('PKG_PN', '')), str(row.get('CHIP_ID', ''))
            
            if device:
                keys = [
                    f"{device}|{stage}|{pkg_pn}|{chip_id}",
                    f"{device}|{stage}|{pkg_pn}",
                    f"{device}|{stage}",
                    f"{device}"
                ]
                
                spec_data = {
                    'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'CHIP_ID': chip_id,
                    'TEST_SPEC': str(row.get('TEST_SPEC', '')),
                    'TEST_TIME': int(row.get('TEST_TIME', 60)),
                    'SPEC_VERSION': str(row.get('SPEC_VERSION', ''))
                }
                
                for key in keys:
                    if key not in test_specs:
                        test_specs[key] = spec_data
        
        return test_specs
    
    def _get_recipe_file_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取工艺配方数据"""
        file_path = os.path.join(self.excel_path, 'ET_RECIPE_FILE.xlsx')
        df = pd.read_excel(file_path)
        
        recipe_files = {}
        for _, row in df.iterrows():
            device, stage = str(row.get('DEVICE', '')), str(row.get('STAGE', ''))
            pkg_pn, chip_id = str(row.get('PKG_PN', '')), str(row.get('CHIP_ID', ''))
            
            if device:
                keys = [
                    f"{device}|{stage}|{pkg_pn}|{chip_id}",
                    f"{device}|{stage}|{pkg_pn}",
                    f"{device}|{stage}",
                    f"{device}"
                ]
                
                recipe_data = {
                    'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'CHIP_ID': chip_id,
                    'RECIPE_FILE': str(row.get('RECIPE_FILE', '')),
                    'RECIPE_VERSION': str(row.get('RECIPE_VERSION', '')),
                    'TESTER_TYPE': str(row.get('TESTER_TYPE', '')),
                    'HANDLER_TYPE': str(row.get('HANDLER_TYPE', ''))
                }
                
                for key in keys:
                    if key not in recipe_files:
                        recipe_files[key] = recipe_data
        
        return recipe_files
    
    def _get_equipment_status_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取设备状态数据"""
        file_path = os.path.join(self.excel_path, 'EQP_STATUS.xlsx')
        df = pd.read_excel(file_path)
        
        equipment_status = {}
        for _, row in df.iterrows():
            eqp_id = str(row.get('EQP_ID', ''))
            eqp_status = str(row.get('EQP_STATUS', ''))
            
            if eqp_id:
                equipment_status[eqp_id] = {
                    'EQP_ID': eqp_id,
                    'EQP_NAME': str(row.get('EQP_NAME', '')),
                    'EQP_STATUS': eqp_status,
                    'TESTER_ID': str(row.get('TESTER_ID', '')),
                    'HANDLER_ID': str(row.get('HANDLER_ID', '')),
                    'FAC_ID': str(row.get('FAC_ID', '')),
                    'UPDATE_TIME': row.get('UPDATE_TIME'),
                    'available': eqp_status.upper() in ['RUN', 'IDLE', 'SETUP', 'READY']
                }
        
        return equipment_status
    
    def _get_uph_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取UPH数据"""
        file_path = os.path.join(self.excel_path, 'ET_UPH_EQP.xlsx')
        df = pd.read_excel(file_path)
        
        uph_data = {}
        for _, row in df.iterrows():
            device, stage = str(row.get('DEVICE', '')), str(row.get('STAGE', ''))
            pkg_pn = str(row.get('PKG_PN', ''))
            uph = row.get('UPH', 0)
            
            if device and uph > 0:
                keys = [
                    f"{device}|{stage}|{pkg_pn}",
                    f"{device}|{stage}",
                    f"{device}"
                ]
                
                uph_info = {
                    'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'UPH': int(uph),
                    'SORTER_MODEL': str(row.get('SORTER_MODEL', '')),
                    'FAC_ID': str(row.get('FAC_ID', '')),
                    'TESTER_TYPE': str(row.get('TESTER_TYPE', '')),
                    'HANDLER_TYPE': str(row.get('HANDLER_TYPE', ''))
                }
                
                for key in keys:
                    if key not in uph_data:
                        uph_data[key] = uph_info
        
        return uph_data
    
    def _get_priority_configs_from_excel(self) -> Dict[str, Dict]:
        """从Excel获取优先级配置数据"""
        device_priority = {}
        lot_priority = {}
        
        # 获取设备优先级配置
        try:
            device_file = os.path.join(self.excel_path, 'devicepriorityconfig.xlsx')
            if os.path.exists(device_file):
                df = pd.read_excel(device_file)
                for _, row in df.iterrows():
                    device, stage = str(row.get('DEVICE', '')), str(row.get('STAGE', ''))
                    if device:
                        keys = [f"{device}|{stage}", f"{device}"]
                        row_dict = row.to_dict()
                        for key in keys:
                            if key not in device_priority:
                                device_priority[key] = row_dict
        except Exception as e:
            logger.warning(f"Excel设备优先级配置读取失败: {e}")
        
        # 获取批次优先级配置
        try:
            lot_file = os.path.join(self.excel_path, 'lotpriorityconfig.xlsx')
            if os.path.exists(lot_file):
                df = pd.read_excel(lot_file)
                for _, row in df.iterrows():
                    lot_id = str(row.get('LOT_ID', ''))
                    if lot_id:
                        lot_priority[lot_id] = row.to_dict()
        except Exception as e:
            logger.warning(f"Excel批次优先级配置读取失败: {e}")
        
        return {'device': device_priority, 'lot': lot_priority}
    
    def _get_tcc_inv_data(self) -> List[Dict]:
        """从MySQL获取套件资源数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 直接查询所有字段
                cursor.execute("SELECT * FROM TCC_INV WHERE id IS NOT NULL ORDER BY id")
                
                tcc_data = []
                for row in cursor.fetchall():
                    tcc_data.append(dict(row))
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(tcc_data)} 条TCC_INV数据")
            return tcc_data
            
        except Exception as e:
            logger.error(f"MySQL获取TCC_INV数据失败: {e}")
            return []
    
    def _get_ct_data(self) -> List[Dict]:
        """从MySQL获取产品周期数据 - 修复字段映射"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 直接查询所有字段
                cursor.execute("SELECT * FROM ct WHERE id IS NOT NULL ORDER BY id")
                
                ct_data = []
                for row in cursor.fetchall():
                    ct_data.append(dict(row))
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(ct_data)} 条CT数据")
            return ct_data
            
        except Exception as e:
            logger.error(f"MySQL获取CT数据失败: {e}")
            return []
    
    def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态信息"""
        return {
            'mysql_available': self.mysql_available,
            'excel_available': self.excel_available,
            'excel_path': self.excel_path,
            'recommended_source': 'MySQL' if self.mysql_available else 'Excel' if self.excel_available else 'None'
        }
    
    def get_table_data(self, table_name: str, page: int = 1, per_page: int = 50, filters: List = None) -> Dict:
        """获取表格数据（API v2兼容方法）"""
        try:
            # 根据table_name路由到对应的方法
            if table_name in ['wait_lot', 'ET_WAIT_LOT', 'et_wait_lot', 'wip_lot']:
                data, source = self.get_wait_lot_data()
                columns = ['LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PKG_PN', 'CHIP_ID', 'CREATE_TIME'] if data else []
            elif table_name in ['eqp_status', 'EQP_STATUS']:
                data_dict, source = self.get_equipment_status_data()
                data = list(data_dict.values()) if data_dict else []
                # 使用实际存在的字段作为列名
                if data:
                    # 从第一条数据获取可用的列
                    first_record = data[0]
                    # 排除调试字段，只显示业务字段
                    columns = [k for k in first_record.keys() if k not in ['available', 'raw_data']]
                else:
                    columns = ['EQP_ID', 'EQP_NAME', 'EQP_STATUS', 'TESTER_ID', 'HANDLER_ID', 'FAC_ID', 'UPDATE_TIME']
            elif table_name in ['ET_UPH_EQP', 'et_uph_eqp']:
                data_dict, source = self.get_uph_data()
                data = list(data_dict.values()) if data_dict else []
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['DEVICE', 'STAGE', 'PKG_PN', 'UPH', 'SORTER_MODEL', 'FAC_ID']
            elif table_name in ['et_ft_test_spec', 'ET_FT_TEST_SPEC']:
                data_dict, source = self.get_test_spec_data()
                data = list(data_dict.values()) if data_dict else []
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['DEVICE', 'STAGE', 'PKG_PN', 'CHIP_ID', 'TEST_SPEC', 'TEST_TIME']
            elif table_name in ['TCC_INV', 'tcc_inv']:
                # 套件资源表 - 从MySQL获取实际数据
                data = self._get_tcc_inv_data()
                source = "MySQL" if self.mysql_available else "Excel" 
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['INVENTORY_ID', 'INVENTORY_TYPE', 'INVENTORY_STATUS', 'LOCATION', 'REMARK']
            elif table_name in ['CT', 'ct']:
                # 产品周期表 - 从MySQL获取实际数据
                data = self._get_ct_data()
                source = "MySQL" if self.mysql_available else "Excel"
                # 使用实际字段作为列名
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['PRODUCT', 'STAGE', 'CT_VALUE', 'UNIT', 'REMARK']
            elif table_name in ['devicepriorityconfig']:
                # 产品优先级配置表
                data = self._get_device_priority_data()
                source = "MySQL" if self.mysql_available else "Excel"
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['ID', 'DEVICE', 'PRIORITY', 'FROM_TIME', 'END_TIME', 'REFRESH_TIME', 'USER']
            elif table_name in ['lotpriorityconfig']:
                # 批次优先级配置表
                data = self._get_lot_priority_data()
                source = "MySQL" if self.mysql_available else "Excel"
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['ID', 'DEVICE', 'STAGE', 'PRIORITY', 'REFRESH_TIME', 'USER']
            elif table_name in ['lotprioritydone']:
                # 已排产批次表
                data = self._get_lotprioritydone_data()
                source = "MySQL" if self.mysql_available else "Excel"
                if data:
                    first_record = data[0]
                    columns = [k for k in first_record.keys() if not k.startswith('_')]
                else:
                    columns = ['id', 'lot_id', 'device', 'stage', 'quantity', 'priority', 'scheduled_time', 'completion_rate', 'status', 'operator']
            else:
                return {
                    'success': False,
                    'error': f'不支持的表格: {table_name}'
                }
            
            # 应用筛选条件
            if filters and len(filters) > 0:
                data = self._apply_filters(data, filters)
                logger.info(f"应用筛选条件后，数据从原始条数筛选到 {len(data)} 条")
            
            # 记录总数（筛选后）
            total = len(data)
            
            # 应用分页
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            paged_data = data[start_idx:end_idx]
            
            logger.info(f"表格 {table_name}: 总计 {total} 条记录, 第 {page} 页显示 {len(paged_data)} 条")
            
            return {
                'success': True,
                'data': paged_data,
                'columns': columns,
                'total': total,
                'pages': (total + per_page - 1) // per_page,
                'data_source': source,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"获取表格数据失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_table_columns(self, table_name: str) -> Dict:
        """获取表格列信息（API v2兼容方法）"""
        try:
            # 扩展列映射
            column_mapping = {
                'wait_lot': ['LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PKG_PN', 'CHIP_ID', 'CREATE_TIME'],
                'ET_WAIT_LOT': ['LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PKG_PN', 'CHIP_ID', 'CREATE_TIME'],
                'wip_lot': ['LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PKG_PN', 'CHIP_ID', 'CREATE_TIME'],
                'eqp_status': ['EQP_ID', 'EQP_NAME', 'EQP_STATUS', 'TESTER_ID', 'HANDLER_ID', 'FAC_ID', 'UPDATE_TIME'],
                'EQP_STATUS': ['EQP_ID', 'EQP_NAME', 'EQP_STATUS', 'TESTER_ID', 'HANDLER_ID', 'FAC_ID', 'UPDATE_TIME'],
                'ET_UPH_EQP': ['DEVICE', 'STAGE', 'PKG_PN', 'UPH', 'SORTER_MODEL', 'FAC_ID', 'TESTER_TYPE', 'HANDLER_TYPE'],
                'et_ft_test_spec': ['DEVICE', 'STAGE', 'PKG_PN', 'CHIP_ID', 'TEST_SPEC', 'TEST_TIME'],
                'ET_FT_TEST_SPEC': ['DEVICE', 'STAGE', 'PKG_PN', 'CHIP_ID', 'TEST_SPEC', 'TEST_TIME'],
                'TCC_INV': ['INVENTORY_ID', 'INVENTORY_TYPE', 'INVENTORY_STATUS', 'LOCATION', 'REMARK'],
                'CT': ['PRODUCT', 'STAGE', 'CT_VALUE', 'UNIT', 'REMARK'],
                'devicepriorityconfig': ['id', 'device', 'priority', 'from_time', 'end_time', 'refresh_time', 'user'],
                'lotpriorityconfig': ['id', 'device', 'stage', 'priority', 'refresh_time', 'user'],
                'lotprioritydone': ['id', 'lot_id', 'device', 'stage', 'quantity', 'priority', 'scheduled_time', 'completion_rate', 'status', 'operator'],
                'et_wait_lot': ['LOT_ID', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PKG_PN', 'CHIP_ID', 'CREATE_TIME'],
                'test_spec': ['SPEC_ID', 'SPEC_NAME', 'TEST_TIME', 'PARAMETER'],
                'recipe_file': ['RECIPE_ID', 'RECIPE_NAME', 'PRODUCT_ID', 'VERSION']
            }
            
            columns = column_mapping.get(table_name, [])
            return {
                'success': True,
                'columns': columns,
                'data_source': 'MySQL' if self.mysql_available else 'Excel'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_available_tables(self) -> Dict:
        """获取可用表格列表（API v2兼容方法）"""
        try:
            tables = [
                'wait_lot', 'ET_WAIT_LOT', 'test_spec', 'recipe_file', 
                'equipment_status', 'uph_data', 'priority_config'
            ]
            return {
                'success': True,
                'tables': tables,
                'data_source': 'MySQL' if self.mysql_available else 'Excel'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_table_data(self, table_name: str, format_type: str = 'excel', filters: List = None) -> Dict:
        """导出表格数据（API v2兼容方法）"""
        try:
            import os
            import pandas as pd
            from flask import current_app
            
            # 获取完整的表格数据（不分页）
            data_result = self.get_table_data(table_name, page=1, per_page=10000, filters=filters)
            
            if not data_result['success']:
                return {
                    'success': False,
                    'error': f'获取数据失败: {data_result.get("error", "未知错误")}'
                }
            
            data = data_result['data']
            columns = data_result['columns']
            total_records = data_result['total']
            
            if not data:
                return {
                    'success': False,
                    'error': '没有数据可以导出'
                }
            
            # 创建导出目录
            export_dir = os.path.join(current_app.root_path, 'static', 'exports')
            os.makedirs(export_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'{table_name}_export_{timestamp}.xlsx'
            filepath = os.path.join(export_dir, filename)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 确保列顺序正确
            if columns:
                # 只保留存在的列
                available_columns = [col for col in columns if col in df.columns]
                if available_columns:
                    df = df[available_columns]
            
            # 导出到Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=table_name, index=False)
                
                # 获取工作表对象并设置列宽
                worksheet = writer.sheets[table_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 生成下载URL
            export_url = f'/static/exports/{filename}'
            
            logger.info(f"成功导出表格 {table_name}，记录数: {total_records}，文件: {filename}")
            
            return {
                'success': True,
                'export_url': export_url,
                'filename': filename,
                'records_count': total_records,
                'format': format_type,
                'table_name': table_name
            }
            
        except Exception as e:
            logger.error(f"导出表格数据失败: {e}")
            return {
                'success': False,
                'error': f'导出失败: {str(e)}'
            }
    
    def create_record(self, table_name: str, data: Dict) -> Dict:
        """创建新记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法创建记录'
                }
            
            # 数据预处理 - 处理日期字段和数据类型
            processed_data = self._preprocess_record_data(table_name, data)
            
            if not processed_data:
                return {
                    'success': False,
                    'error': '没有有效的数据可以插入'
                }
            
            # 确定使用哪个数据库
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps_system'
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                # 构建插入语句
                columns = list(processed_data.keys())
                placeholders = ', '.join(['%s'] * len(columns))
                column_names = ', '.join(columns)
                
                sql = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"
                values = list(processed_data.values())
                
                logger.debug(f"执行插入SQL: {sql}")
                logger.debug(f"插入数据: {values}")
                
                cursor.execute(sql, values)
                connection.commit()
                
                # 获取插入的记录ID
                record_id = cursor.lastrowid
                
            connection.close()
            
            # 清理缓存
            self.clear_cache()
            
            logger.info(f"成功创建{table_name}记录，ID: {record_id}")
            return {
                'success': True,
                'record_id': record_id,
                'message': '记录创建成功'
            }
            
        except Exception as e:
            logger.error(f"创建{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'创建记录失败: {str(e)}'
            }
    
    def update_record(self, table_name: str, data: Dict) -> Dict:
        """更新记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法更新记录'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            
            primary_key = field_config['primary_key']
            
            if primary_key not in data:
                return {
                    'success': False,
                    'error': f'缺少主键字段: {primary_key}'
                }
            
            # 保存主键值
            primary_key_value = data[primary_key]
            
            # 数据预处理 - 处理日期字段和数据类型
            processed_data = self._preprocess_record_data(table_name, data)
            
            # 移除主键，避免更新主键
            if primary_key in processed_data:
                processed_data.pop(primary_key)
            
            if not processed_data:  # 如果除了主键没有其他字段
                return {
                    'success': False,
                    'error': '没有要更新的字段'
                }
            
            # 确定使用哪个数据库
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps_system'
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                # 构建更新语句
                set_clauses = ', '.join([f"{col} = %s" for col in processed_data.keys()])
                sql = f"UPDATE {table_name} SET {set_clauses} WHERE {primary_key} = %s"
                
                values = list(processed_data.values()) + [primary_key_value]
                
                logger.debug(f"执行更新SQL: {sql}")
                logger.debug(f"更新数据: {values}")
                
                cursor.execute(sql, values)
                affected_rows = cursor.rowcount
                connection.commit()
                
            connection.close()
            
            # 清理缓存
            self.clear_cache()
            
            logger.info(f"成功更新{table_name}记录，主键: {primary_key_value}，影响行数: {affected_rows}")
            return {
                'success': True,
                'affected_rows': affected_rows,
                'message': '记录更新成功'
            }
            
        except Exception as e:
            logger.error(f"更新{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'更新记录失败: {str(e)}'
            }
    
    def delete_record(self, table_name: str, record_id: str) -> Dict:
        """删除单条记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法删除记录'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            
            primary_key = field_config['primary_key']
            
            # 确定使用哪个数据库
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps_system'
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                sql = f"DELETE FROM {table_name} WHERE {primary_key} = %s"
                cursor.execute(sql, [record_id])
                affected_rows = cursor.rowcount
                connection.commit()
                
            connection.close()
            
            # 清理缓存
            self.clear_cache()
            
            logger.info(f"成功删除{table_name}记录，主键: {record_id}，影响行数: {affected_rows}")
            return {
                'success': True,
                'affected_rows': affected_rows,
                'message': '记录删除成功'
            }
            
        except Exception as e:
            logger.error(f"删除{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'删除记录失败: {str(e)}'
            }
    
    def batch_delete_records(self, table_name: str, record_ids: List[str]) -> Dict:
        """批量删除记录"""
        try:
            if not self.mysql_available:
                return {
                    'success': False,
                    'error': 'MySQL数据源不可用，无法删除记录'
                }
            
            if not record_ids:
                return {
                    'success': False,
                    'error': '没有提供要删除的记录ID'
                }
            
            # 获取表的主键配置
            field_config = UNIQUE_FIELD_MAPPING.get(table_name, {
                'primary_key': 'id',
                'business_key': 'id',
                'display_key': 'id'
            })
            
            primary_key = field_config['primary_key']
            
            # 处理主键类型 - 特别是字符串类型的LOT_ID
            processed_ids = []
            for record_id in record_ids:
                # 确保记录ID是正确的格式
                if primary_key == 'LOT_ID' or 'LOT_ID' in primary_key:
                    # LOT_ID通常是字符串，保持原样
                    processed_ids.append(str(record_id))
                else:
                    # 数值型ID，尝试转换
                    try:
                        processed_ids.append(int(record_id))
                    except (ValueError, TypeError):
                        processed_ids.append(str(record_id))
            
            logger.debug(f"批量删除 {table_name}，主键: {primary_key}，ID列表: {processed_ids}")
            
            # 确定使用哪个数据库
            database = 'aps'  # 默认数据库
            if table_name in ['devicepriorityconfig', 'lotpriorityconfig']:
                database = 'aps_system'
            elif table_name in ['lotprioritydone']:
                database = 'aps'  # 已排产批次表在aps数据库
            
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database=database,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                # 构建批量删除语句
                placeholders = ', '.join(['%s'] * len(processed_ids))
                sql = f"DELETE FROM {table_name} WHERE {primary_key} IN ({placeholders})"
                
                logger.debug(f"执行批量删除SQL: {sql}")
                logger.debug(f"删除ID列表: {processed_ids}")
                
                cursor.execute(sql, processed_ids)
                deleted_count = cursor.rowcount
                connection.commit()
                
            connection.close()
            
            # 清理缓存
            self.clear_cache()
            
            logger.info(f"成功批量删除{table_name}记录，删除数量: {deleted_count}")
            return {
                'success': True,
                'deleted_count': deleted_count,
                'message': f'成功删除 {deleted_count} 条记录'
            }
            
        except Exception as e:
            logger.error(f"批量删除{table_name}记录失败: {e}")
            return {
                'success': False,
                'error': f'批量删除失败: {str(e)}'
            }
    def _get_device_priority_data(self) -> List[Dict]:
        """获取产品优先级配置数据"""
        try:
            if self.mysql_available:
                return self._get_device_priority_from_mysql()
            elif self.excel_available:
                return self._get_device_priority_from_excel()
            else:
                return []
        except Exception as e:
            logger.error(f"获取产品优先级数据失败: {e}")
            return []
    
    def _get_lot_priority_data(self) -> List[Dict]:
        """获取批次优先级配置数据"""
        try:
            if self.mysql_available:
                return self._get_lot_priority_from_mysql()
            elif self.excel_available:
                return self._get_lot_priority_from_excel()
            else:
                return []
        except Exception as e:
            logger.error(f"获取批次优先级数据失败: {e}")
            return []
    
    def _get_lotprioritydone_data(self) -> List[Dict]:
        """获取已排产批次数据"""
        try:
            if self.mysql_available:
                return self._get_lotprioritydone_from_mysql()
            else:
                return []
        except Exception as e:
            logger.error(f"获取已排产批次数据失败: {e}")
            return []
    
    def _get_device_priority_from_mysql(self) -> List[Dict]:
        """从MySQL获取产品优先级配置数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps_system',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT * FROM devicepriorityconfig ORDER BY id")
                data = []
                for row in cursor.fetchall():
                    record = dict(row)
                    for time_field in ['from_time', 'end_time', 'refresh_time', 'created_at', 'updated_at']:
                        if record.get(time_field):
                            record[time_field] = str(record[time_field])
                    data.append(record)
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(data)} 条产品优先级配置数据")
            return data
            
        except Exception as e:
            logger.error(f"MySQL获取产品优先级配置数据失败: {e}")
            return []
    
    def _get_lot_priority_from_mysql(self) -> List[Dict]:
        """从MySQL获取批次优先级配置数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps_system',
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT * FROM lotpriorityconfig ORDER BY id")
                data = []
                for row in cursor.fetchall():
                    record = dict(row)
                    for time_field in ['refresh_time', 'created_at', 'updated_at']:
                        if record.get(time_field):
                            record[time_field] = str(record[time_field])
                    data.append(record)
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(data)} 条批次优先级配置数据")
            return data
            
        except Exception as e:
            logger.error(f"MySQL获取批次优先级配置数据失败: {e}")
            return []
    
    def _get_device_priority_from_excel(self) -> List[Dict]:
        """从Excel获取产品优先级配置数据"""
        try:
            file_path = os.path.join(self.excel_path, 'devicepriorityconfig.xlsx')
            if not os.path.exists(file_path):
                logger.warning(f"Excel文件不存在: {file_path}")
                return []
            
            df = pd.read_excel(file_path)
            data = []
            for _, row in df.iterrows():
                record = {
                    'id': row.get('ID'),
                    'device': str(row.get('DEVICE', '')),
                    'priority': str(row.get('PRIORITY', '')),
                    'from_time': str(row.get('FROM_TIME', '')) if pd.notna(row.get('FROM_TIME')) else None,
                    'end_time': str(row.get('END_TIME', '')) if pd.notna(row.get('END_TIME')) else None,
                    'refresh_time': str(row.get('REFRESH_TIME', '')) if pd.notna(row.get('REFRESH_TIME')) else None,
                    'user': str(row.get('USER', '')) if pd.notna(row.get('USER')) else None
                }
                data.append(record)
            
            logger.info(f"从Excel获取到 {len(data)} 条产品优先级配置数据")
            return data
            
        except Exception as e:
            logger.error(f"Excel获取产品优先级配置数据失败: {e}")
            return []
    
    def _get_lot_priority_from_excel(self) -> List[Dict]:
        """从Excel获取批次优先级配置数据"""
        try:
            file_path = os.path.join(self.excel_path, 'lotpriorityconfig.xlsx')
            if not os.path.exists(file_path):
                logger.warning(f"Excel文件不存在: {file_path}")
                return []
            
            df = pd.read_excel(file_path)
            data = []
            for _, row in df.iterrows():
                record = {
                    'id': row.get('ID'),
                    'device': str(row.get('DEVICE', '')),
                    'stage': str(row.get('STAGE', '')) if pd.notna(row.get('STAGE')) else None,
                    'priority': str(row.get('PRIORITY', '')),
                    'refresh_time': str(row.get('REFRESH_TIME', '')) if pd.notna(row.get('REFRESH_TIME')) else None,
                    'user': str(row.get('USER', '')) if pd.notna(row.get('USER')) else None
                }
                data.append(record)
            
            logger.info(f"从Excel获取到 {len(data)} 条批次优先级配置数据")
            return data
            
        except Exception as e:
            logger.error(f"Excel获取批次优先级配置数据失败: {e}")
            return []
    
    def _get_lotprioritydone_from_mysql(self) -> List[Dict]:
        """从MySQL获取已排产批次数据"""
        try:
            import pymysql
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='WWWwww123!',
                database='aps',  # 已排产批次表在aps数据库
                charset='utf8mb4'
            )
            
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT * FROM lotprioritydone 
                    ORDER BY scheduled_time DESC, id DESC
                    LIMIT 1000
                """)
                
                lotprioritydone_data = []
                for row in cursor.fetchall():
                    record = dict(row)
                    # 转换时间字段为字符串格式
                    for time_field in ['scheduled_time', 'refresh_time', 'created_at', 'updated_at']:
                        if record.get(time_field):
                            record[time_field] = str(record[time_field])
                    lotprioritydone_data.append(record)
            
            connection.close()
            logger.info(f"从MySQL获取到 {len(lotprioritydone_data)} 条已排产批次数据")
            return lotprioritydone_data
            
        except Exception as e:
            logger.error(f"MySQL获取已排产批次数据失败: {e}")
            return []
