#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单数据管理功能测试脚本
测试步骤3中所有订单数据管理相关的功能
"""

import requests
import json
import time
from datetime import datetime

class OrderDataFunctionTest:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.logged_in = False
        
    def log_test(self, test_name, status, message="", details=None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⏭️"
        print(f"{status_icon} {test_name}: {message}")
        
    def login(self, username="admin", password="admin"):
        """登录系统"""
        try:
            login_page = self.session.get(f"{self.base_url}/auth/login")
            if login_page.status_code != 200:
                return False
                
            login_data = {'username': username, 'password': password}
            response = self.session.post(f"{self.base_url}/auth/login", 
                                       data=login_data, allow_redirects=True)
            
            test_response = self.session.get(f"{self.base_url}/orders/semi-auto")
            if test_response.status_code == 200 and "手动导入订单" in test_response.text:
                self.logged_in = True
                return True
            return False
        except:
            return False
    
    def test_ft_order_data(self):
        """测试FT订单数据获取"""
        print("\n🔍 测试FT订单数据获取...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/v2/orders/data/ft_summary")
            
            print(f"   FT数据响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        orders = data.get('data', [])
                        self.log_test("获取FT订单数据", "PASS", 
                                    f"成功获取 {len(orders)} 条FT订单数据")
                        return orders
                    else:
                        self.log_test("获取FT订单数据", "FAIL", 
                                    f"获取失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("获取FT订单数据", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("获取FT订单数据", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("获取FT订单数据", "FAIL", f"请求异常: {str(e)}")
        
        return []
    
    def test_cp_order_data(self):
        """测试CP订单数据获取"""
        print("\n🔍 测试CP订单数据获取...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/v2/orders/data/cp_summary")
            
            print(f"   CP数据响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应数据: {data}")
                    
                    if data.get('success'):
                        orders = data.get('data', [])
                        self.log_test("获取CP订单数据", "PASS", 
                                    f"成功获取 {len(orders)} 条CP订单数据")
                        return orders
                    else:
                        self.log_test("获取CP订单数据", "FAIL", 
                                    f"获取失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("获取CP订单数据", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("获取CP订单数据", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("获取CP订单数据", "FAIL", f"请求异常: {str(e)}")
        
        return []
    
    def test_order_data_filtering(self):
        """测试订单数据筛选功能"""
        print("\n🔍 测试订单数据筛选...")
        
        # 测试日期范围筛选
        filter_params = {
            "start_date": "2025-06-01",
            "end_date": "2025-06-30",
            "status": "pending",
            "order_type": "FT"
        }
        
        try:
            response = self.session.get(f"{self.base_url}/api/v2/orders/data/ft_summary",
                                      params=filter_params)
            
            print(f"   筛选响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   筛选结果: {data}")
                    
                    if data.get('success'):
                        filtered_orders = data.get('data', [])
                        self.log_test("订单数据筛选", "PASS", 
                                    f"筛选功能正常，返回 {len(filtered_orders)} 条数据")
                        return True
                    else:
                        self.log_test("订单数据筛选", "FAIL", 
                                    f"筛选失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("订单数据筛选", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("订单数据筛选", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("订单数据筛选", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_order_data_export(self):
        """测试订单数据导出功能"""
        print("\n🔍 测试订单数据导出...")
        
        export_data = {
            "order_type": "FT",
            "format": "excel",
            "selected_ids": [],  # 空数组表示导出全部
            "include_fields": ["order_id", "product_name", "quantity", "delivery_date"]
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/data/export",
                                       json=export_data,
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   导出响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 检查响应是否为文件下载
                content_type = response.headers.get('Content-Type', '')
                if 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type:
                    self.log_test("订单数据导出", "PASS", 
                                f"成功导出Excel文件，大小: {len(response.content)} 字节")
                    return True
                else:
                    # 可能是JSON响应
                    try:
                        data = response.json()
                        if data.get('success'):
                            self.log_test("订单数据导出", "PASS", 
                                        f"导出请求成功: {data.get('message', '无消息')}")
                            return True
                        else:
                            self.log_test("订单数据导出", "FAIL", 
                                        f"导出失败: {data.get('error', '未知错误')}")
                    except:
                        self.log_test("订单数据导出", "FAIL", "响应格式异常")
            else:
                self.log_test("订单数据导出", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("订单数据导出", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_order_data_refresh(self):
        """测试订单数据刷新功能"""
        print("\n🔍 测试订单数据刷新...")
        
        try:
            response = self.session.post(f"{self.base_url}/api/v2/orders/data/refresh",
                                       json={},
                                       headers={'Content-Type': 'application/json'})
            
            print(f"   刷新响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   刷新结果: {data}")
                    
                    if data.get('success'):
                        self.log_test("订单数据刷新", "PASS", 
                                    f"数据刷新成功: {data.get('message', '无消息')}")
                        return True
                    else:
                        self.log_test("订单数据刷新", "FAIL", 
                                    f"刷新失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("订单数据刷新", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("订单数据刷新", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("订单数据刷新", "FAIL", f"请求异常: {str(e)}")
        
        return False
    
    def test_order_statistics(self):
        """测试订单统计信息"""
        print("\n🔍 测试订单统计信息...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/v2/orders/statistics")
            
            print(f"   统计响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   统计数据: {data}")
                    
                    if data.get('success'):
                        stats = data.get('data', {})
                        self.log_test("订单统计信息", "PASS", 
                                    f"成功获取统计信息: 总数 {stats.get('total', 0)}")
                        return stats
                    else:
                        self.log_test("订单统计信息", "FAIL", 
                                    f"获取失败: {data.get('error', '未知错误')}")
                except Exception as e:
                    self.log_test("订单统计信息", "FAIL", f"解析响应失败: {str(e)}")
            else:
                self.log_test("订单统计信息", "FAIL", 
                            f"HTTP错误: {response.status_code}")
                            
        except Exception as e:
            self.log_test("订单统计信息", "FAIL", f"请求异常: {str(e)}")
        
        return {}
    
    def run_all_tests(self):
        """运行所有订单数据管理功能测试"""
        print("🚀 开始订单数据管理功能测试...")
        print("=" * 60)
        
        # 登录
        if not self.login():
            print("❌ 无法登录系统")
            return
        
        # 获取FT订单数据
        ft_orders = self.test_ft_order_data()
        
        # 获取CP订单数据
        cp_orders = self.test_cp_order_data()
        
        # 测试筛选功能
        self.test_order_data_filtering()
        
        # 测试导出功能
        self.test_order_data_export()
        
        # 测试刷新功能
        self.test_order_data_refresh()
        
        # 测试统计信息
        self.test_order_statistics()
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 订单数据管理功能测试结果")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        skipped_tests = len([r for r in self.test_results if r['status'] == 'SKIP'])
        
        print(f"总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"⏭️ 跳过: {skipped_tests}")
        
        if total_tests > 0:
            print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  - {result['test_name']}: {result['message']}")

if __name__ == "__main__":
    test_suite = OrderDataFunctionTest()
    test_suite.run_all_tests()
